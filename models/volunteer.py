"""
Volunteer class for the Code Clinics application.
"""

from datetime import datetime
from typing import Dict, Any, List
from .user import User


class Volunteer(User):
    """Volunteer class representing mentors offering help."""
    
    def __init__(self, username: str, email: str, full_name: str,
                 expertise_areas: List[str] = None, experience_level: str = "intermediate"):
        """
        Initialize a new volunteer.
        
        Args:
            username (str): Unique username
            email (str): Email address
            full_name (str): Full name
            expertise_areas (List[str]): Areas of expertise
            experience_level (str): Experience level (intermediate, advanced, expert)
        """
        super().__init__(username, email, full_name)
        self.expertise_areas = expertise_areas or []
        self.experience_level = experience_level
        self.programming_languages = []  # Languages the volunteer can help with
        self.availability_hours = []  # Available time slots
        self.sessions_conducted = []  # List of session IDs
        self.rating = 0.0  # Average rating from students
        self.total_ratings = 0
        self.is_available = True  # Current availability status
        
    def get_role(self) -> str:
        """Return the user's role."""
        return "Volunteer"
    
    def get_capabilities(self) -> List[str]:
        """Return list of volunteer capabilities."""
        return [
            "accept_help_requests",
            "conduct_sessions",
            "set_availability",
            "view_student_requests",
            "update_expertise",
            "view_session_history"
        ]
    
    def add_expertise_area(self, area: str):
        """Add an area of expertise."""
        if area not in self.expertise_areas:
            self.expertise_areas.append(area)
    
    def remove_expertise_area(self, area: str):
        """Remove an area of expertise."""
        if area in self.expertise_areas:
            self.expertise_areas.remove(area)
    
    def add_programming_language(self, language: str):
        """Add a programming language to expertise."""
        if language not in self.programming_languages:
            self.programming_languages.append(language)
    
    def set_availability(self, available: bool):
        """Set current availability status."""
        self.is_available = available
    
    def add_availability_hours(self, hours: List[str]):
        """Add available time slots."""
        for hour in hours:
            if hour not in self.availability_hours:
                self.availability_hours.append(hour)
    
    def conduct_session(self, session_id: str):
        """Record that volunteer conducted a session."""
        if session_id not in self.sessions_conducted:
            self.sessions_conducted.append(session_id)
    
    def add_rating(self, rating: float):
        """
        Add a new rating from a student.
        
        Args:
            rating (float): Rating from 1.0 to 5.0
        """
        if 1.0 <= rating <= 5.0:
            total_score = self.rating * self.total_ratings
            self.total_ratings += 1
            self.rating = (total_score + rating) / self.total_ratings
    
    def can_help_with(self, topic: str, language: str = None) -> bool:
        """
        Check if volunteer can help with a specific topic/language.
        
        Args:
            topic (str): Topic needing help
            language (str): Programming language (optional)
            
        Returns:
            bool: True if volunteer can help
        """
        topic_match = any(area.lower() in topic.lower() for area in self.expertise_areas)
        
        if language:
            language_match = any(lang.lower() == language.lower() for lang in self.programming_languages)
            return topic_match and language_match
        
        return topic_match
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert volunteer object to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'expertise_areas': self.expertise_areas,
            'experience_level': self.experience_level,
            'programming_languages': self.programming_languages,
            'availability_hours': self.availability_hours,
            'sessions_conducted': self.sessions_conducted,
            'rating': self.rating,
            'total_ratings': self.total_ratings,
            'is_available': self.is_available
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create volunteer object from dictionary."""
        volunteer = cls(
            username=data['username'],
            email=data['email'],
            full_name=data['full_name'],
            expertise_areas=data.get('expertise_areas', []),
            experience_level=data.get('experience_level', 'intermediate')
        )
        
        # Restore additional attributes
        volunteer.user_id = data['user_id']
        volunteer.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('last_login'):
            volunteer.last_login = datetime.fromisoformat(data['last_login'])
        volunteer.is_active = data.get('is_active', True)
        volunteer.programming_languages = data.get('programming_languages', [])
        volunteer.availability_hours = data.get('availability_hours', [])
        volunteer.sessions_conducted = data.get('sessions_conducted', [])
        volunteer.rating = data.get('rating', 0.0)
        volunteer.total_ratings = data.get('total_ratings', 0)
        volunteer.is_available = data.get('is_available', True)
        
        return volunteer
