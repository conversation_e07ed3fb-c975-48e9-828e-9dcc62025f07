"""
TimeSlot class for managing volunteer availability slots.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import uuid


class TimeSlot:
    """Represents a 30-minute time slot that volunteers can create and students can book."""
    
    def __init__(self, volunteer_id: str, start_time: datetime, topic: str = "", 
                 description: str = ""):
        """
        Initialize a new time slot.
        
        Args:
            volunteer_id (str): ID of the volunteer creating the slot
            start_time (datetime): Start time of the slot
            topic (str): Optional topic for the slot
            description (str): Optional description of what help is offered
        """
        self.slot_id = str(uuid.uuid4())
        self.volunteer_id = volunteer_id
        self.start_time = start_time
        self.end_time = start_time + timedelta(minutes=30)  # Fixed 30-minute slots
        self.topic = topic
        self.description = description
        self.created_at = datetime.now()
        self.status = "available"  # available, booked, completed, cancelled
        self.student_id = None  # Set when booked
        self.booked_at = None
        self.calendar_event_id = None  # Google Calendar event ID
        self.session_notes = ""
        self.student_rating = None  # Rating given by student (1-5)
        self.volunteer_rating = None  # Rating given by volunteer (1-5)
        self.student_feedback = ""
        self.volunteer_feedback = ""
        
    def is_available(self) -> bool:
        """Check if the slot is available for booking."""
        return self.status == "available" and self.student_id is None
    
    def is_booked(self) -> bool:
        """Check if the slot is booked."""
        return self.status == "booked" and self.student_id is not None
    
    def is_in_past(self) -> bool:
        """Check if the slot is in the past."""
        return self.start_time < datetime.now()
    
    def can_be_booked(self) -> bool:
        """Check if the slot can be booked (available and not in past)."""
        return self.is_available() and not self.is_in_past()
    
    def book_slot(self, student_id: str) -> bool:
        """
        Book the slot for a student.
        
        Args:
            student_id (str): ID of the student booking the slot
            
        Returns:
            bool: True if booking successful
        """
        if not self.can_be_booked():
            return False
        
        self.student_id = student_id
        self.status = "booked"
        self.booked_at = datetime.now()
        return True
    
    def cancel_booking(self) -> bool:
        """
        Cancel the booking (can be done by student or volunteer).
        
        Returns:
            bool: True if cancellation successful
        """
        if self.status == "booked":
            self.student_id = None
            self.status = "available"
            self.booked_at = None
            return True
        return False
    
    def complete_session(self, notes: str = "") -> bool:
        """
        Mark the session as completed.
        
        Args:
            notes (str): Session notes
            
        Returns:
            bool: True if completion successful
        """
        if self.status == "booked":
            self.status = "completed"
            self.session_notes = notes
            return True
        return False
    
    def cancel_slot(self) -> bool:
        """
        Cancel the entire slot (volunteer cancels their availability).
        
        Returns:
            bool: True if cancellation successful
        """
        if self.status in ["available", "booked"]:
            self.status = "cancelled"
            return True
        return False
    
    def add_student_rating(self, rating: int, feedback: str = "") -> bool:
        """
        Add student rating for the session.
        
        Args:
            rating (int): Rating from 1 to 5
            feedback (str): Optional feedback
            
        Returns:
            bool: True if rating added successfully
        """
        if not (1 <= rating <= 5):
            return False
        
        self.student_rating = rating
        self.student_feedback = feedback
        return True
    
    def add_volunteer_rating(self, rating: int, feedback: str = "") -> bool:
        """
        Add volunteer rating for the student.
        
        Args:
            rating (int): Rating from 1 to 5
            feedback (str): Optional feedback
            
        Returns:
            bool: True if rating added successfully
        """
        if not (1 <= rating <= 5):
            return False
        
        self.volunteer_rating = rating
        self.volunteer_feedback = feedback
        return True
    
    def set_calendar_event_id(self, event_id: str):
        """Set the Google Calendar event ID."""
        self.calendar_event_id = event_id
    
    def get_duration_minutes(self) -> int:
        """Get the duration of the slot in minutes."""
        return 30  # Fixed 30-minute slots
    
    def get_formatted_time(self) -> str:
        """Get formatted time string for display."""
        return f"{self.start_time.strftime('%H:%M')} - {self.end_time.strftime('%H:%M')}"
    
    def get_formatted_date(self) -> str:
        """Get formatted date string for display."""
        return self.start_time.strftime('%Y-%m-%d')
    
    def get_day_name(self) -> str:
        """Get day name for display."""
        return self.start_time.strftime('%A')
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert time slot object to dictionary."""
        return {
            'slot_id': self.slot_id,
            'volunteer_id': self.volunteer_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'topic': self.topic,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'status': self.status,
            'student_id': self.student_id,
            'booked_at': self.booked_at.isoformat() if self.booked_at else None,
            'calendar_event_id': self.calendar_event_id,
            'session_notes': self.session_notes,
            'student_rating': self.student_rating,
            'volunteer_rating': self.volunteer_rating,
            'student_feedback': self.student_feedback,
            'volunteer_feedback': self.volunteer_feedback
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TimeSlot':
        """Create time slot object from dictionary."""
        slot = cls(
            volunteer_id=data['volunteer_id'],
            start_time=datetime.fromisoformat(data['start_time']),
            topic=data.get('topic', ''),
            description=data.get('description', '')
        )
        
        slot.slot_id = data['slot_id']
        slot.end_time = datetime.fromisoformat(data['end_time'])
        slot.created_at = datetime.fromisoformat(data['created_at'])
        slot.status = data['status']
        slot.student_id = data.get('student_id')
        slot.booked_at = datetime.fromisoformat(data['booked_at']) if data.get('booked_at') else None
        slot.calendar_event_id = data.get('calendar_event_id')
        slot.session_notes = data.get('session_notes', '')
        slot.student_rating = data.get('student_rating')
        slot.volunteer_rating = data.get('volunteer_rating')
        slot.student_feedback = data.get('student_feedback', '')
        slot.volunteer_feedback = data.get('volunteer_feedback', '')
        
        return slot
    
    def __str__(self) -> str:
        """String representation of the time slot."""
        status_emoji = {
            'available': '🟢',
            'booked': '🔴',
            'completed': '✅',
            'cancelled': '❌'
        }
        
        return f"{status_emoji.get(self.status, '⚪')} {self.get_formatted_time()} - {self.topic or 'General Help'}"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"TimeSlot(id={self.slot_id[:8]}, volunteer={self.volunteer_id[:8]}, time={self.get_formatted_time()}, status={self.status})"
