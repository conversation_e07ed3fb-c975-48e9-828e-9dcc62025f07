"""
Student class for the Code Clinics application.
"""

from datetime import datetime
from typing import Dict, Any, List
from .user import User


class Student(User):
    """Student class representing learners seeking help."""
    
    def __init__(self, username: str, email: str, full_name: str, 
                 skill_level: str = "beginner", learning_goals: List[str] = None):
        """
        Initialize a new student.
        
        Args:
            username (str): Unique username
            email (str): Email address
            full_name (str): Full name
            skill_level (str): Current skill level (beginner, intermediate, advanced)
            learning_goals (List[str]): List of learning goals/topics of interest
        """
        super().__init__(username, email, full_name)
        self.skill_level = skill_level
        self.learning_goals = learning_goals or []
        self.help_requests = []  # List of help request IDs
        self.completed_sessions = []  # List of completed session IDs
        self.preferred_languages = []  # Programming languages of interest
        
    def get_role(self) -> str:
        """Return the user's role."""
        return "Student"
    
    def get_capabilities(self) -> List[str]:
        """Return list of student capabilities."""
        return [
            "request_help",
            "join_session",
            "rate_volunteer",
            "view_session_history",
            "update_learning_goals"
        ]
    
    def add_learning_goal(self, goal: str):
        """Add a learning goal."""
        if goal not in self.learning_goals:
            self.learning_goals.append(goal)
    
    def remove_learning_goal(self, goal: str):
        """Remove a learning goal."""
        if goal in self.learning_goals:
            self.learning_goals.remove(goal)
    
    def add_preferred_language(self, language: str):
        """Add a preferred programming language."""
        if language not in self.preferred_languages:
            self.preferred_languages.append(language)
    
    def request_help(self, topic: str, description: str, urgency: str = "normal") -> str:
        """
        Create a help request.
        
        Args:
            topic (str): Topic needing help with
            description (str): Detailed description of the problem
            urgency (str): Urgency level (low, normal, high)
            
        Returns:
            str: Help request ID
        """
        import uuid
        request_id = str(uuid.uuid4())
        help_request = {
            'request_id': request_id,
            'student_id': self.user_id,
            'topic': topic,
            'description': description,
            'urgency': urgency,
            'created_at': datetime.now().isoformat(),
            'status': 'open'
        }
        self.help_requests.append(request_id)
        return request_id
    
    def complete_session(self, session_id: str):
        """Mark a session as completed."""
        if session_id not in self.completed_sessions:
            self.completed_sessions.append(session_id)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert student object to dictionary."""
        base_dict = super().to_dict()
        base_dict.update({
            'skill_level': self.skill_level,
            'learning_goals': self.learning_goals,
            'help_requests': self.help_requests,
            'completed_sessions': self.completed_sessions,
            'preferred_languages': self.preferred_languages
        })
        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create student object from dictionary."""
        student = cls(
            username=data['username'],
            email=data['email'],
            full_name=data['full_name'],
            skill_level=data.get('skill_level', 'beginner'),
            learning_goals=data.get('learning_goals', [])
        )
        
        # Restore additional attributes
        student.user_id = data['user_id']
        student.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('last_login'):
            student.last_login = datetime.fromisoformat(data['last_login'])
        student.is_active = data.get('is_active', True)
        student.help_requests = data.get('help_requests', [])
        student.completed_sessions = data.get('completed_sessions', [])
        student.preferred_languages = data.get('preferred_languages', [])
        
        return student
