"""
SlotManager class for managing time slots and calendar integration.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from .time_slot import TimeSlot
from .user import User
from .student import Student
from .volunteer import Volunteer

try:
    from utils.calendar_integration import CalendarManager
    CALENDAR_AVAILABLE = True
except ImportError:
    CALENDAR_AVAILABLE = False


class SlotManager:
    """Manages time slots and calendar integration for Code Clinics."""
    
    # Working hours: 08:00 to 15:00 (8 AM to 3 PM)
    WORK_START_HOUR = 8
    WORK_END_HOUR = 15
    SLOT_DURATION_MINUTES = 30
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize the slot manager.
        
        Args:
            data_dir (str): Directory to store data files
        """
        self.data_dir = data_dir
        self.slots_file = os.path.join(data_dir, "time_slots.json")
        
        # In-memory storage
        self.slots: Dict[str, TimeSlot] = {}  # slot_id -> TimeSlot object
        
        # Calendar integration
        self.calendar_manager = None
        if CALENDAR_AVAILABLE:
            self.calendar_manager = CalendarManager()
        
        # Ensure data directory exists
        os.makedirs(data_dir, exist_ok=True)
        
        # Load existing slots
        self.load_slots()
    
    def load_slots(self):
        """Load slots from JSON file."""
        if os.path.exists(self.slots_file):
            try:
                with open(self.slots_file, 'r') as f:
                    slots_data = json.load(f)
                    for slot_data in slots_data:
                        slot = TimeSlot.from_dict(slot_data)
                        self.slots[slot.slot_id] = slot
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error loading slots: {e}")
    
    def save_slots(self):
        """Save slots to JSON file."""
        slots_data = [slot.to_dict() for slot in self.slots.values()]
        with open(self.slots_file, 'w') as f:
            json.dump(slots_data, f, indent=2)
    
    def get_working_hours_slots(self, date: datetime) -> List[datetime]:
        """
        Get all possible 30-minute slots for a working day.
        
        Args:
            date (datetime): The date to get slots for
            
        Returns:
            List[datetime]: List of slot start times
        """
        slots = []
        start_time = date.replace(hour=self.WORK_START_HOUR, minute=0, second=0, microsecond=0)
        end_time = date.replace(hour=self.WORK_END_HOUR, minute=0, second=0, microsecond=0)
        
        current_time = start_time
        while current_time < end_time:
            slots.append(current_time)
            current_time += timedelta(minutes=self.SLOT_DURATION_MINUTES)
        
        return slots
    
    def get_next_working_days(self, days: int = 5) -> List[datetime]:
        """
        Get the next working days (Monday to Friday).
        
        Args:
            days (int): Number of working days to get
            
        Returns:
            List[datetime]: List of working day dates
        """
        working_days = []
        current_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Start from tomorrow if it's past working hours today
        if datetime.now().hour >= self.WORK_END_HOUR:
            current_date += timedelta(days=1)
        
        while len(working_days) < days:
            # Monday = 0, Sunday = 6
            if current_date.weekday() < 5:  # Monday to Friday
                working_days.append(current_date)
            current_date += timedelta(days=1)
        
        return working_days
    
    def create_slot(self, volunteer_id: str, start_time: datetime, topic: str = "", 
                   description: str = "") -> Tuple[bool, str]:
        """
        Create a new time slot.
        
        Args:
            volunteer_id (str): ID of the volunteer
            start_time (datetime): Start time of the slot
            topic (str): Optional topic
            description (str): Optional description
            
        Returns:
            Tuple[bool, str]: (success, message)
        """
        # Validate time is in working hours
        if not self.is_valid_slot_time(start_time):
            return False, "Slot must be between 08:00 and 15:00 on weekdays"
        
        # Check if slot is in the past
        if start_time < datetime.now():
            return False, "Cannot create slots for past times"
        
        # Check if slot already exists for this volunteer at this time
        if self.slot_exists(volunteer_id, start_time):
            return False, "You already have a slot at this time"
        
        # Create the slot
        slot = TimeSlot(volunteer_id, start_time, topic, description)
        self.slots[slot.slot_id] = slot
        
        # Create calendar event if available
        if self.calendar_manager and self.calendar_manager.service:
            event_id = self.create_calendar_slot(slot)
            if event_id:
                slot.set_calendar_event_id(event_id)
        
        self.save_slots()
        return True, f"Slot created successfully for {slot.get_formatted_time()}"
    
    def is_valid_slot_time(self, start_time: datetime) -> bool:
        """
        Check if the time is valid for creating slots.
        
        Args:
            start_time (datetime): The start time to check
            
        Returns:
            bool: True if valid
        """
        # Check if it's a weekday
        if start_time.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False
        
        # Check if it's within working hours
        if start_time.hour < self.WORK_START_HOUR or start_time.hour >= self.WORK_END_HOUR:
            return False
        
        # Check if minutes are 0 or 30 (for 30-minute slots)
        if start_time.minute not in [0, 30]:
            return False
        
        return True
    
    def slot_exists(self, volunteer_id: str, start_time: datetime) -> bool:
        """
        Check if a slot already exists for a volunteer at a specific time.
        
        Args:
            volunteer_id (str): Volunteer ID
            start_time (datetime): Start time to check
            
        Returns:
            bool: True if slot exists
        """
        for slot in self.slots.values():
            if (slot.volunteer_id == volunteer_id and 
                slot.start_time == start_time and 
                slot.status != "cancelled"):
                return True
        return False
    
    def get_available_slots(self, days_ahead: int = 5) -> List[TimeSlot]:
        """
        Get all available slots for the next working days.
        
        Args:
            days_ahead (int): Number of days to look ahead
            
        Returns:
            List[TimeSlot]: Available slots
        """
        available_slots = []
        cutoff_time = datetime.now() + timedelta(days=days_ahead)
        
        for slot in self.slots.values():
            if (slot.can_be_booked() and 
                slot.start_time <= cutoff_time):
                available_slots.append(slot)
        
        # Sort by start time
        available_slots.sort(key=lambda s: s.start_time)
        return available_slots
    
    def get_volunteer_slots(self, volunteer_id: str, days_ahead: int = 5) -> List[TimeSlot]:
        """
        Get all slots for a specific volunteer.
        
        Args:
            volunteer_id (str): Volunteer ID
            days_ahead (int): Number of days to look ahead
            
        Returns:
            List[TimeSlot]: Volunteer's slots
        """
        volunteer_slots = []
        cutoff_time = datetime.now() + timedelta(days=days_ahead)
        
        for slot in self.slots.values():
            if (slot.volunteer_id == volunteer_id and 
                slot.start_time <= cutoff_time and
                slot.status != "cancelled"):
                volunteer_slots.append(slot)
        
        # Sort by start time
        volunteer_slots.sort(key=lambda s: s.start_time)
        return volunteer_slots
    
    def get_student_bookings(self, student_id: str, days_ahead: int = 5) -> List[TimeSlot]:
        """
        Get all bookings for a specific student.
        
        Args:
            student_id (str): Student ID
            days_ahead (int): Number of days to look ahead
            
        Returns:
            List[TimeSlot]: Student's bookings
        """
        student_bookings = []
        cutoff_time = datetime.now() + timedelta(days=days_ahead)
        
        for slot in self.slots.values():
            if (slot.student_id == student_id and 
                slot.start_time <= cutoff_time):
                student_bookings.append(slot)
        
        # Sort by start time
        student_bookings.sort(key=lambda s: s.start_time)
        return student_bookings
    
    def book_slot(self, slot_id: str, student_id: str) -> Tuple[bool, str]:
        """
        Book a slot for a student.
        
        Args:
            slot_id (str): Slot ID
            student_id (str): Student ID
            
        Returns:
            Tuple[bool, str]: (success, message)
        """
        if slot_id not in self.slots:
            return False, "Slot not found"
        
        slot = self.slots[slot_id]
        
        if not slot.can_be_booked():
            return False, "Slot is not available for booking"
        
        # Book the slot
        if slot.book_slot(student_id):
            # Update calendar event if available
            if self.calendar_manager and slot.calendar_event_id:
                self.update_calendar_slot_booking(slot, student_id)
            
            self.save_slots()
            return True, f"Slot booked successfully for {slot.get_formatted_time()} on {slot.get_formatted_date()}"
        
        return False, "Failed to book slot"
    
    def cancel_booking(self, slot_id: str, user_id: str) -> Tuple[bool, str]:
        """
        Cancel a booking (can be done by student or volunteer).
        
        Args:
            slot_id (str): Slot ID
            user_id (str): User ID (student or volunteer)
            
        Returns:
            Tuple[bool, str]: (success, message)
        """
        if slot_id not in self.slots:
            return False, "Slot not found"
        
        slot = self.slots[slot_id]
        
        # Check if user has permission to cancel
        if user_id not in [slot.student_id, slot.volunteer_id]:
            return False, "You don't have permission to cancel this booking"
        
        if slot.cancel_booking():
            # Update calendar event if available
            if self.calendar_manager and slot.calendar_event_id:
                self.update_calendar_slot_cancellation(slot)
            
            self.save_slots()
            return True, "Booking cancelled successfully"
        
        return False, "Failed to cancel booking"
    
    def create_calendar_slot(self, slot: TimeSlot) -> Optional[str]:
        """
        Create a calendar event for a slot.
        
        Args:
            slot (TimeSlot): The slot to create an event for
            
        Returns:
            str: Event ID if successful, None otherwise
        """
        if not self.calendar_manager:
            return None
        
        # This will be implemented with the calendar integration
        # For now, return a placeholder
        return f"cal_event_{slot.slot_id[:8]}"
    
    def update_calendar_slot_booking(self, slot: TimeSlot, student_id: str):
        """Update calendar event when slot is booked."""
        # Implementation will be added with calendar integration
        pass
    
    def update_calendar_slot_cancellation(self, slot: TimeSlot):
        """Update calendar event when booking is cancelled."""
        # Implementation will be added with calendar integration
        pass
