"""
Session class for the Code Clinics application.
"""

from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import uuid


class Session:
    """Represents a code clinic session between a volunteer and student."""
    
    def __init__(self, student_id: str, volunteer_id: str, topic: str, 
                 description: str, scheduled_time: datetime = None):
        """
        Initialize a new session.
        
        Args:
            student_id (str): ID of the student
            volunteer_id (str): ID of the volunteer
            topic (str): Topic of the session
            description (str): Description of what help is needed
            scheduled_time (datetime): When the session is scheduled
        """
        self.session_id = str(uuid.uuid4())
        self.student_id = student_id
        self.volunteer_id = volunteer_id
        self.topic = topic
        self.description = description
        self.scheduled_time = scheduled_time or datetime.now()
        self.created_at = datetime.now()
        self.started_at = None
        self.ended_at = None
        self.status = "scheduled"  # scheduled, in_progress, completed, cancelled
        self.notes = ""
        self.student_rating = None  # Rating given by student (1-5)
        self.volunteer_rating = None  # Rating given by volunteer (1-5)
        self.student_feedback = ""
        self.volunteer_feedback = ""
        self.programming_language = None
        self.difficulty_level = "medium"  # easy, medium, hard
        
    def start_session(self):
        """Start the session."""
        if self.status == "scheduled":
            self.status = "in_progress"
            self.started_at = datetime.now()
            return True
        return False
    
    def end_session(self, notes: str = ""):
        """End the session."""
        if self.status == "in_progress":
            self.status = "completed"
            self.ended_at = datetime.now()
            self.notes = notes
            return True
        return False
    
    def cancel_session(self, reason: str = ""):
        """Cancel the session."""
        if self.status in ["scheduled", "in_progress"]:
            self.status = "cancelled"
            self.notes = f"Cancelled: {reason}" if reason else "Cancelled"
            return True
        return False
    
    def add_student_rating(self, rating: int, feedback: str = ""):
        """
        Add rating and feedback from student.
        
        Args:
            rating (int): Rating from 1 to 5
            feedback (str): Optional feedback text
        """
        if 1 <= rating <= 5:
            self.student_rating = rating
            self.student_feedback = feedback
            return True
        return False
    
    def add_volunteer_rating(self, rating: int, feedback: str = ""):
        """
        Add rating and feedback from volunteer.
        
        Args:
            rating (int): Rating from 1 to 5
            feedback (str): Optional feedback text
        """
        if 1 <= rating <= 5:
            self.volunteer_rating = rating
            self.volunteer_feedback = feedback
            return True
        return False
    
    def get_duration(self) -> Optional[timedelta]:
        """Get session duration if completed."""
        if self.started_at and self.ended_at:
            return self.ended_at - self.started_at
        return None
    
    def is_active(self) -> bool:
        """Check if session is currently active."""
        return self.status == "in_progress"
    
    def is_completed(self) -> bool:
        """Check if session is completed."""
        return self.status == "completed"
    
    def is_scheduled(self) -> bool:
        """Check if session is scheduled."""
        return self.status == "scheduled"
    
    def set_programming_language(self, language: str):
        """Set the programming language for this session."""
        self.programming_language = language
    
    def set_difficulty_level(self, level: str):
        """Set the difficulty level (easy, medium, hard)."""
        if level in ["easy", "medium", "hard"]:
            self.difficulty_level = level
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session object to dictionary."""
        return {
            'session_id': self.session_id,
            'student_id': self.student_id,
            'volunteer_id': self.volunteer_id,
            'topic': self.topic,
            'description': self.description,
            'scheduled_time': self.scheduled_time.isoformat(),
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'status': self.status,
            'notes': self.notes,
            'student_rating': self.student_rating,
            'volunteer_rating': self.volunteer_rating,
            'student_feedback': self.student_feedback,
            'volunteer_feedback': self.volunteer_feedback,
            'programming_language': self.programming_language,
            'difficulty_level': self.difficulty_level
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create session object from dictionary."""
        session = cls(
            student_id=data['student_id'],
            volunteer_id=data['volunteer_id'],
            topic=data['topic'],
            description=data['description'],
            scheduled_time=datetime.fromisoformat(data['scheduled_time'])
        )
        
        # Restore all attributes
        session.session_id = data['session_id']
        session.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('started_at'):
            session.started_at = datetime.fromisoformat(data['started_at'])
        if data.get('ended_at'):
            session.ended_at = datetime.fromisoformat(data['ended_at'])
        session.status = data['status']
        session.notes = data.get('notes', '')
        session.student_rating = data.get('student_rating')
        session.volunteer_rating = data.get('volunteer_rating')
        session.student_feedback = data.get('student_feedback', '')
        session.volunteer_feedback = data.get('volunteer_feedback', '')
        session.programming_language = data.get('programming_language')
        session.difficulty_level = data.get('difficulty_level', 'medium')
        
        return session
    
    def __str__(self) -> str:
        """String representation of the session."""
        return f"Session: {self.topic} ({self.status}) - {self.scheduled_time.strftime('%Y-%m-%d %H:%M')}"
    
    def __repr__(self) -> str:
        """Developer representation of the session."""
        return f"<Session(id={self.session_id}, topic={self.topic}, status={self.status})>"
