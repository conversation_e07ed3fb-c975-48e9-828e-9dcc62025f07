"""
ClinicManager class for managing the Code Clinics application.
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union, Tuple
from .user import User
from .student import Student
from .volunteer import Volunteer
from .session import Session
from .slot_manager import Slot<PERSON>anager
from .time_slot import TimeSlot

try:
    from utils.calendar_integration import CalendarManager
    CALENDAR_AVAILABLE = True
except ImportError:
    CALENDAR_AVAILABLE = False
    print("Calendar integration not available. Install Google Calendar API dependencies.")


class ClinicManager:
    """Main manager class for the Code Clinics application."""
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize the clinic manager.
        
        Args:
            data_dir (str): Directory to store data files
        """
        self.data_dir = data_dir
        self.users_file = os.path.join(data_dir, "users.json")
        self.sessions_file = os.path.join(data_dir, "sessions.json")
        self.help_requests_file = os.path.join(data_dir, "help_requests.json")
        
        # In-memory storage
        self.users: Dict[str, User] = {}  # user_id -> User object
        self.sessions: Dict[str, Session] = {}  # session_id -> Session object
        self.help_requests: Dict[str, Dict] = {}  # request_id -> help request data
        self.current_user: Optional[User] = None

        # Calendar integration
        self.calendar_manager = None
        if CALENDAR_AVAILABLE:
            self.calendar_manager = CalendarManager()

        # Slot management
        self.slot_manager = SlotManager(data_dir)

        # Ensure data directory exists
        os.makedirs(data_dir, exist_ok=True)

        # Load existing data
        self.load_data()
    
    def load_data(self):
        """Load data from JSON files."""
        # Load users
        if os.path.exists(self.users_file):
            try:
                with open(self.users_file, 'r') as f:
                    users_data = json.load(f)
                    for user_data in users_data:
                        if user_data['role'] == 'Student':
                            user = Student.from_dict(user_data)
                        elif user_data['role'] == 'Volunteer':
                            user = Volunteer.from_dict(user_data)
                        else:
                            continue
                        self.users[user.user_id] = user
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error loading users: {e}")
        
        # Load sessions
        if os.path.exists(self.sessions_file):
            try:
                with open(self.sessions_file, 'r') as f:
                    sessions_data = json.load(f)
                    for session_data in sessions_data:
                        session = Session.from_dict(session_data)
                        self.sessions[session.session_id] = session
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error loading sessions: {e}")
        
        # Load help requests
        if os.path.exists(self.help_requests_file):
            try:
                with open(self.help_requests_file, 'r') as f:
                    self.help_requests = json.load(f)
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error loading help requests: {e}")
    
    def save_data(self):
        """Save data to JSON files."""
        # Save users
        users_data = [user.to_dict() for user in self.users.values()]
        with open(self.users_file, 'w') as f:
            json.dump(users_data, f, indent=2)
        
        # Save sessions
        sessions_data = [session.to_dict() for session in self.sessions.values()]
        with open(self.sessions_file, 'w') as f:
            json.dump(sessions_data, f, indent=2)
        
        # Save help requests
        with open(self.help_requests_file, 'w') as f:
            json.dump(self.help_requests, f, indent=2)
    
    def register_user(self, username: str, email: str, full_name: str, 
                     role: str, **kwargs) -> bool:
        """
        Register a new user.
        
        Args:
            username (str): Unique username
            email (str): Email address
            full_name (str): Full name
            role (str): User role ('student' or 'volunteer')
            **kwargs: Additional role-specific parameters
            
        Returns:
            bool: True if registration successful
        """
        # Check if username already exists
        if any(user.username == username for user in self.users.values()):
            return False
        
        # Create user based on role
        if role.lower() == 'student':
            user = Student(
                username=username,
                email=email,
                full_name=full_name,
                skill_level=kwargs.get('skill_level', 'beginner'),
                learning_goals=kwargs.get('learning_goals', [])
            )
        elif role.lower() == 'volunteer':
            user = Volunteer(
                username=username,
                email=email,
                full_name=full_name,
                expertise_areas=kwargs.get('expertise_areas', []),
                experience_level=kwargs.get('experience_level', 'intermediate')
            )
        else:
            return False
        
        self.users[user.user_id] = user
        self.save_data()
        return True
    
    def login_user(self, username: str) -> bool:
        """
        Login a user by username.
        
        Args:
            username (str): Username to login
            
        Returns:
            bool: True if login successful
        """
        for user in self.users.values():
            if user.username == username and user.is_active:
                user.login()
                self.current_user = user
                self.save_data()
                return True
        return False
    
    def logout_user(self):
        """Logout the current user."""
        self.current_user = None
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        for user in self.users.values():
            if user.username == username:
                return user
        return None
    
    def get_students(self) -> List[Student]:
        """Get all students."""
        return [user for user in self.users.values() if isinstance(user, Student)]
    
    def get_volunteers(self) -> List[Volunteer]:
        """Get all volunteers."""
        return [user for user in self.users.values() if isinstance(user, Volunteer)]
    
    def get_available_volunteers(self) -> List[Volunteer]:
        """Get all available volunteers."""
        return [vol for vol in self.get_volunteers() if vol.is_available]

    def create_help_request(self, topic: str, description: str,
                          urgency: str = "normal") -> Optional[str]:
        """
        Create a help request (only for students).

        Args:
            topic (str): Topic needing help
            description (str): Detailed description
            urgency (str): Urgency level

        Returns:
            str: Request ID if successful, None otherwise
        """
        if not isinstance(self.current_user, Student):
            return None

        request_id = self.current_user.request_help(topic, description, urgency)

        # Store the help request
        self.help_requests[request_id] = {
            'request_id': request_id,
            'student_id': self.current_user.user_id,
            'student_name': self.current_user.full_name,
            'topic': topic,
            'description': description,
            'urgency': urgency,
            'created_at': datetime.now().isoformat(),
            'status': 'open'
        }

        self.save_data()
        return request_id

    def get_open_help_requests(self) -> List[Dict]:
        """Get all open help requests."""
        return [req for req in self.help_requests.values() if req['status'] == 'open']

    def accept_help_request(self, request_id: str) -> Optional[str]:
        """
        Accept a help request and create a session (only for volunteers).

        Args:
            request_id (str): Help request ID

        Returns:
            str: Session ID if successful, None otherwise
        """
        if not isinstance(self.current_user, Volunteer):
            return None

        if request_id not in self.help_requests:
            return None

        help_request = self.help_requests[request_id]
        if help_request['status'] != 'open':
            return None

        # Create a new session
        session = Session(
            student_id=help_request['student_id'],
            volunteer_id=self.current_user.user_id,
            topic=help_request['topic'],
            description=help_request['description']
        )

        self.sessions[session.session_id] = session

        # Update help request status
        help_request['status'] = 'accepted'
        help_request['volunteer_id'] = self.current_user.user_id
        help_request['session_id'] = session.session_id

        self.save_data()
        return session.session_id

    def get_user_sessions(self, user_id: str = None) -> List[Session]:
        """Get sessions for a specific user or current user."""
        if user_id is None and self.current_user:
            user_id = self.current_user.user_id

        if not user_id:
            return []

        return [session for session in self.sessions.values()
                if session.student_id == user_id or session.volunteer_id == user_id]

    def start_session(self, session_id: str) -> bool:
        """Start a session."""
        if session_id in self.sessions:
            return self.sessions[session_id].start_session()
        return False

    def end_session(self, session_id: str, notes: str = "") -> bool:
        """End a session."""
        if session_id in self.sessions:
            success = self.sessions[session_id].end_session(notes)
            if success:
                self.save_data()
            return success
        return False

    def rate_session(self, session_id: str, rating: int, feedback: str = "") -> bool:
        """
        Rate a session (from current user's perspective).

        Args:
            session_id (str): Session ID
            rating (int): Rating from 1 to 5
            feedback (str): Optional feedback

        Returns:
            bool: True if rating successful
        """
        if session_id not in self.sessions or not self.current_user:
            return False

        session = self.sessions[session_id]

        if isinstance(self.current_user, Student) and session.student_id == self.current_user.user_id:
            success = session.add_student_rating(rating, feedback)
            # Update volunteer's rating
            if success and session.volunteer_id in self.users:
                volunteer = self.users[session.volunteer_id]
                if isinstance(volunteer, Volunteer):
                    volunteer.add_rating(rating)
        elif isinstance(self.current_user, Volunteer) and session.volunteer_id == self.current_user.user_id:
            success = session.add_volunteer_rating(rating, feedback)
        else:
            return False

        if success:
            self.save_data()
        return success

    def get_session_by_id(self, session_id: str) -> Optional[Session]:
        """Get session by ID."""
        return self.sessions.get(session_id)

    def find_volunteers_for_topic(self, topic: str, language: str = None) -> List[Volunteer]:
        """Find volunteers who can help with a specific topic/language."""
        available_volunteers = self.get_available_volunteers()
        return [vol for vol in available_volunteers if vol.can_help_with(topic, language)]

    def authenticate_calendar(self, user_email: str) -> Tuple[bool, str]:
        """
        Authenticate with Google Calendar for the given user.

        Args:
            user_email (str): User's email address

        Returns:
            tuple[bool, str]: (success, message)
        """
        if not self.calendar_manager:
            return False, "Calendar integration not available."

        success = self.calendar_manager.authenticate(user_email)
        if success:
            # Get or create the Code Clinics calendar
            calendar_id = self.calendar_manager.get_or_create_calendar()
            if calendar_id:
                return True, f"Connected to Code Clinics calendar: {calendar_id}"
            else:
                return False, "Failed to create or access calendar"

        return False, "Authentication failed"

    def schedule_session_with_calendar(self, session_id: str, scheduled_time: datetime,
                                     duration_minutes: int = 60) -> Tuple[bool, str]:
        """
        Schedule a session with Google Calendar integration.

        Args:
            session_id (str): Session ID
            scheduled_time (datetime): When to schedule the session
            duration_minutes (int): Session duration in minutes

        Returns:
            Tuple[bool, str]: (success, message)
        """
        if session_id not in self.sessions:
            return False, "Session not found"

        session = self.sessions[session_id]

        # Update session timing
        session.scheduled_time = scheduled_time
        session.set_duration(duration_minutes)

        # Create calendar event if calendar is available
        if self.calendar_manager and self.calendar_manager.service:
            student = self.users.get(session.student_id)
            volunteer = self.users.get(session.volunteer_id)

            if student and volunteer:
                session_data = {
                    'session_id': session.session_id,
                    'topic': session.topic,
                    'description': session.description,
                    'student_name': student.full_name,
                    'student_email': student.email,
                    'volunteer_name': volunteer.full_name,
                    'volunteer_email': volunteer.email,
                    'scheduled_time': scheduled_time,
                    'duration_minutes': duration_minutes
                }

                event_id = self.calendar_manager.create_session_event(session_data)
                if event_id:
                    session.set_calendar_event_id(event_id)
                    self.save_data()
                    return True, f"Calendar event created: {event_id}"
                else:
                    self.save_data()
                    return False, "Failed to create calendar event"
            else:
                self.save_data()
                return False, "Student or volunteer not found"
        else:
            self.save_data()
            return True, "Session scheduled (calendar not available)"

    def update_session_calendar_status(self, session_id: str, status: str, notes: str = "") -> bool:
        """
        Update session status in both local storage and calendar.

        Args:
            session_id (str): Session ID
            status (str): New status (completed, cancelled)
            notes (str): Additional notes

        Returns:
            bool: True if successful
        """
        if session_id not in self.sessions:
            return False

        session = self.sessions[session_id]

        # Update calendar event if available
        if (self.calendar_manager and self.calendar_manager.service and
            session.calendar_event_id):

            updates = {'status': status}
            if notes:
                updates['notes'] = notes

            success = self.calendar_manager.update_session_event(
                session.calendar_event_id, updates
            )

            if success:
                print(f"Calendar event updated for session {session_id}")
            else:
                print(f"Failed to update calendar event for session {session_id}")

        return True

    def cancel_session_with_calendar(self, session_id: str, reason: str = "") -> bool:
        """
        Cancel a session and update calendar.

        Args:
            session_id (str): Session ID
            reason (str): Cancellation reason

        Returns:
            bool: True if successful
        """
        if session_id not in self.sessions:
            return False

        session = self.sessions[session_id]

        # Cancel the session
        if session.cancel_session(reason):
            # Update calendar
            self.update_session_calendar_status(session_id, 'cancelled', reason)
            self.save_data()
            return True

        return False

    def get_upcoming_calendar_sessions(self, days_ahead: int = 7) -> List[Dict]:
        """
        Get upcoming sessions from Google Calendar.

        Args:
            days_ahead (int): Number of days to look ahead

        Returns:
            List[Dict]: List of upcoming sessions
        """
        if not self.calendar_manager or not self.calendar_manager.service:
            return []

        return self.calendar_manager.get_upcoming_sessions(days_ahead)

    def is_calendar_authenticated(self) -> bool:
        """Check if calendar is authenticated and available."""
        return (self.calendar_manager is not None and
                self.calendar_manager.service is not None)

    def get_calendar_info(self) -> Dict[str, str]:
        """Get calendar integration information."""
        if not self.calendar_manager:
            return {'status': 'not_available', 'message': 'Calendar integration not installed'}

        if not self.calendar_manager.service:
            return {'status': 'not_authenticated', 'message': 'Not authenticated with Google Calendar'}

        return {
            'status': 'authenticated',
            'message': 'Connected to Google Calendar',
            'calendar_id': self.calendar_manager.calendar_id,
            'authenticated_user': self.calendar_manager.authenticated_user
        }

    # Slot Management Methods

    def create_time_slot(self, start_time: datetime, topic: str = "",
                        description: str = "") -> Tuple[bool, str]:
        """
        Create a time slot (only for volunteers).

        Args:
            start_time (datetime): Start time of the slot
            topic (str): Optional topic
            description (str): Optional description

        Returns:
            Tuple[bool, str]: (success, message)
        """
        if not isinstance(self.current_user, Volunteer):
            return False, "Only volunteers can create time slots"

        return self.slot_manager.create_slot(
            self.current_user.user_id, start_time, topic, description
        )

    def book_time_slot(self, slot_id: str) -> Tuple[bool, str]:
        """
        Book a time slot (only for students).

        Args:
            slot_id (str): Slot ID to book

        Returns:
            Tuple[bool, str]: (success, message)
        """
        if not isinstance(self.current_user, Student):
            return False, "Only students can book time slots"

        return self.slot_manager.book_slot(slot_id, self.current_user.user_id)

    def cancel_slot_booking(self, slot_id: str) -> Tuple[bool, str]:
        """
        Cancel a slot booking.

        Args:
            slot_id (str): Slot ID to cancel

        Returns:
            Tuple[bool, str]: (success, message)
        """
        if not self.current_user:
            return False, "No user logged in"

        return self.slot_manager.cancel_booking(slot_id, self.current_user.user_id)

    def get_available_slots(self, days_ahead: int = 5) -> List[TimeSlot]:
        """Get all available slots for the next working days."""
        return self.slot_manager.get_available_slots(days_ahead)

    def get_my_slots(self, days_ahead: int = 5) -> List[TimeSlot]:
        """Get current user's slots (volunteer) or bookings (student)."""
        if not self.current_user:
            return []

        if isinstance(self.current_user, Volunteer):
            return self.slot_manager.get_volunteer_slots(self.current_user.user_id, days_ahead)
        elif isinstance(self.current_user, Student):
            return self.slot_manager.get_student_bookings(self.current_user.user_id, days_ahead)

        return []

    def get_slot_by_id(self, slot_id: str) -> Optional[TimeSlot]:
        """Get a slot by its ID."""
        return self.slot_manager.slots.get(slot_id)

    def complete_slot_session(self, slot_id: str, notes: str = "") -> Tuple[bool, str]:
        """
        Mark a slot session as completed.

        Args:
            slot_id (str): Slot ID
            notes (str): Session notes

        Returns:
            Tuple[bool, str]: (success, message)
        """
        slot = self.get_slot_by_id(slot_id)
        if not slot:
            return False, "Slot not found"

        # Check if current user is the volunteer for this slot
        if not isinstance(self.current_user, Volunteer) or slot.volunteer_id != self.current_user.user_id:
            return False, "Only the volunteer can complete the session"

        if slot.complete_session(notes):
            self.slot_manager.save_slots()
            return True, "Session completed successfully"

        return False, "Failed to complete session"

    def rate_slot_session(self, slot_id: str, rating: int, feedback: str = "") -> Tuple[bool, str]:
        """
        Rate a completed slot session.

        Args:
            slot_id (str): Slot ID
            rating (int): Rating from 1 to 5
            feedback (str): Optional feedback

        Returns:
            Tuple[bool, str]: (success, message)
        """
        slot = self.get_slot_by_id(slot_id)
        if not slot:
            return False, "Slot not found"

        if not self.current_user:
            return False, "No user logged in"

        # Students rate volunteers, volunteers rate students
        if isinstance(self.current_user, Student) and slot.student_id == self.current_user.user_id:
            if slot.add_student_rating(rating, feedback):
                # Update volunteer's overall rating
                volunteer = self.users.get(slot.volunteer_id)
                if volunteer and isinstance(volunteer, Volunteer):
                    volunteer.add_rating(rating)
                self.slot_manager.save_slots()
                self.save_data()
                return True, "Rating submitted successfully"
        elif isinstance(self.current_user, Volunteer) and slot.volunteer_id == self.current_user.user_id:
            if slot.add_volunteer_rating(rating, feedback):
                self.slot_manager.save_slots()
                return True, "Rating submitted successfully"
        else:
            return False, "You can only rate sessions you participated in"

        return False, "Failed to submit rating"
