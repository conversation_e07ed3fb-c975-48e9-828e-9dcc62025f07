"""
ClinicManager class for managing the Code Clinics application.
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Union
from .user import User
from .student import Student
from .volunteer import Volunteer
from .session import Session


class ClinicManager:
    """Main manager class for the Code Clinics application."""
    
    def __init__(self, data_dir: str = "data"):
        """
        Initialize the clinic manager.
        
        Args:
            data_dir (str): Directory to store data files
        """
        self.data_dir = data_dir
        self.users_file = os.path.join(data_dir, "users.json")
        self.sessions_file = os.path.join(data_dir, "sessions.json")
        self.help_requests_file = os.path.join(data_dir, "help_requests.json")
        
        # In-memory storage
        self.users: Dict[str, User] = {}  # user_id -> User object
        self.sessions: Dict[str, Session] = {}  # session_id -> Session object
        self.help_requests: Dict[str, Dict] = {}  # request_id -> help request data
        self.current_user: Optional[User] = None
        
        # Ensure data directory exists
        os.makedirs(data_dir, exist_ok=True)
        
        # Load existing data
        self.load_data()
    
    def load_data(self):
        """Load data from JSON files."""
        # Load users
        if os.path.exists(self.users_file):
            try:
                with open(self.users_file, 'r') as f:
                    users_data = json.load(f)
                    for user_data in users_data:
                        if user_data['role'] == 'Student':
                            user = Student.from_dict(user_data)
                        elif user_data['role'] == 'Volunteer':
                            user = Volunteer.from_dict(user_data)
                        else:
                            continue
                        self.users[user.user_id] = user
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error loading users: {e}")
        
        # Load sessions
        if os.path.exists(self.sessions_file):
            try:
                with open(self.sessions_file, 'r') as f:
                    sessions_data = json.load(f)
                    for session_data in sessions_data:
                        session = Session.from_dict(session_data)
                        self.sessions[session.session_id] = session
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error loading sessions: {e}")
        
        # Load help requests
        if os.path.exists(self.help_requests_file):
            try:
                with open(self.help_requests_file, 'r') as f:
                    self.help_requests = json.load(f)
            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error loading help requests: {e}")
    
    def save_data(self):
        """Save data to JSON files."""
        # Save users
        users_data = [user.to_dict() for user in self.users.values()]
        with open(self.users_file, 'w') as f:
            json.dump(users_data, f, indent=2)
        
        # Save sessions
        sessions_data = [session.to_dict() for session in self.sessions.values()]
        with open(self.sessions_file, 'w') as f:
            json.dump(sessions_data, f, indent=2)
        
        # Save help requests
        with open(self.help_requests_file, 'w') as f:
            json.dump(self.help_requests, f, indent=2)
    
    def register_user(self, username: str, email: str, full_name: str, 
                     role: str, **kwargs) -> bool:
        """
        Register a new user.
        
        Args:
            username (str): Unique username
            email (str): Email address
            full_name (str): Full name
            role (str): User role ('student' or 'volunteer')
            **kwargs: Additional role-specific parameters
            
        Returns:
            bool: True if registration successful
        """
        # Check if username already exists
        if any(user.username == username for user in self.users.values()):
            return False
        
        # Create user based on role
        if role.lower() == 'student':
            user = Student(
                username=username,
                email=email,
                full_name=full_name,
                skill_level=kwargs.get('skill_level', 'beginner'),
                learning_goals=kwargs.get('learning_goals', [])
            )
        elif role.lower() == 'volunteer':
            user = Volunteer(
                username=username,
                email=email,
                full_name=full_name,
                expertise_areas=kwargs.get('expertise_areas', []),
                experience_level=kwargs.get('experience_level', 'intermediate')
            )
        else:
            return False
        
        self.users[user.user_id] = user
        self.save_data()
        return True
    
    def login_user(self, username: str) -> bool:
        """
        Login a user by username.
        
        Args:
            username (str): Username to login
            
        Returns:
            bool: True if login successful
        """
        for user in self.users.values():
            if user.username == username and user.is_active:
                user.login()
                self.current_user = user
                self.save_data()
                return True
        return False
    
    def logout_user(self):
        """Logout the current user."""
        self.current_user = None
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        for user in self.users.values():
            if user.username == username:
                return user
        return None
    
    def get_students(self) -> List[Student]:
        """Get all students."""
        return [user for user in self.users.values() if isinstance(user, Student)]
    
    def get_volunteers(self) -> List[Volunteer]:
        """Get all volunteers."""
        return [user for user in self.users.values() if isinstance(user, Volunteer)]
    
    def get_available_volunteers(self) -> List[Volunteer]:
        """Get all available volunteers."""
        return [vol for vol in self.get_volunteers() if vol.is_available]

    def create_help_request(self, topic: str, description: str,
                          urgency: str = "normal") -> Optional[str]:
        """
        Create a help request (only for students).

        Args:
            topic (str): Topic needing help
            description (str): Detailed description
            urgency (str): Urgency level

        Returns:
            str: Request ID if successful, None otherwise
        """
        if not isinstance(self.current_user, Student):
            return None

        request_id = self.current_user.request_help(topic, description, urgency)

        # Store the help request
        self.help_requests[request_id] = {
            'request_id': request_id,
            'student_id': self.current_user.user_id,
            'student_name': self.current_user.full_name,
            'topic': topic,
            'description': description,
            'urgency': urgency,
            'created_at': datetime.now().isoformat(),
            'status': 'open'
        }

        self.save_data()
        return request_id

    def get_open_help_requests(self) -> List[Dict]:
        """Get all open help requests."""
        return [req for req in self.help_requests.values() if req['status'] == 'open']

    def accept_help_request(self, request_id: str) -> Optional[str]:
        """
        Accept a help request and create a session (only for volunteers).

        Args:
            request_id (str): Help request ID

        Returns:
            str: Session ID if successful, None otherwise
        """
        if not isinstance(self.current_user, Volunteer):
            return None

        if request_id not in self.help_requests:
            return None

        help_request = self.help_requests[request_id]
        if help_request['status'] != 'open':
            return None

        # Create a new session
        session = Session(
            student_id=help_request['student_id'],
            volunteer_id=self.current_user.user_id,
            topic=help_request['topic'],
            description=help_request['description']
        )

        self.sessions[session.session_id] = session

        # Update help request status
        help_request['status'] = 'accepted'
        help_request['volunteer_id'] = self.current_user.user_id
        help_request['session_id'] = session.session_id

        self.save_data()
        return session.session_id

    def get_user_sessions(self, user_id: str = None) -> List[Session]:
        """Get sessions for a specific user or current user."""
        if user_id is None and self.current_user:
            user_id = self.current_user.user_id

        if not user_id:
            return []

        return [session for session in self.sessions.values()
                if session.student_id == user_id or session.volunteer_id == user_id]

    def start_session(self, session_id: str) -> bool:
        """Start a session."""
        if session_id in self.sessions:
            return self.sessions[session_id].start_session()
        return False

    def end_session(self, session_id: str, notes: str = "") -> bool:
        """End a session."""
        if session_id in self.sessions:
            success = self.sessions[session_id].end_session(notes)
            if success:
                self.save_data()
            return success
        return False

    def rate_session(self, session_id: str, rating: int, feedback: str = "") -> bool:
        """
        Rate a session (from current user's perspective).

        Args:
            session_id (str): Session ID
            rating (int): Rating from 1 to 5
            feedback (str): Optional feedback

        Returns:
            bool: True if rating successful
        """
        if session_id not in self.sessions or not self.current_user:
            return False

        session = self.sessions[session_id]

        if isinstance(self.current_user, Student) and session.student_id == self.current_user.user_id:
            success = session.add_student_rating(rating, feedback)
            # Update volunteer's rating
            if success and session.volunteer_id in self.users:
                volunteer = self.users[session.volunteer_id]
                if isinstance(volunteer, Volunteer):
                    volunteer.add_rating(rating)
        elif isinstance(self.current_user, Volunteer) and session.volunteer_id == self.current_user.user_id:
            success = session.add_volunteer_rating(rating, feedback)
        else:
            return False

        if success:
            self.save_data()
        return success

    def get_session_by_id(self, session_id: str) -> Optional[Session]:
        """Get session by ID."""
        return self.sessions.get(session_id)

    def find_volunteers_for_topic(self, topic: str, language: str = None) -> List[Volunteer]:
        """Find volunteers who can help with a specific topic/language."""
        available_volunteers = self.get_available_volunteers()
        return [vol for vol in available_volunteers if vol.can_help_with(topic, language)]
