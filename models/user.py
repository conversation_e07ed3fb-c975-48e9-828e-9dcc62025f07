"""
Base User class for the Code Clinics application.
"""

from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any
import uuid


class User(ABC):
    """Abstract base class for all users in the Code Clinics system."""
    
    def __init__(self, username: str, email: str, full_name: str):
        """
        Initialize a new user.
        
        Args:
            username (str): Unique username for the user
            email (str): User's email address
            full_name (str): User's full name
        """
        self.user_id = str(uuid.uuid4())
        self.username = username
        self.email = email
        self.full_name = full_name
        self.created_at = datetime.now()
        self.last_login = None
        self.is_active = True
    
    def login(self):
        """Record user login."""
        self.last_login = datetime.now()
    
    def deactivate(self):
        """Deactivate the user account."""
        self.is_active = False
    
    def activate(self):
        """Activate the user account."""
        self.is_active = True
    
    @abstractmethod
    def get_role(self) -> str:
        """Return the user's role."""
        pass
    
    @abstractmethod
    def get_capabilities(self) -> list:
        """Return list of user capabilities based on role."""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user object to dictionary for serialization."""
        return {
            'user_id': self.user_id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'created_at': self.created_at.isoformat(),
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'is_active': self.is_active,
            'role': self.get_role()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        """Create user object from dictionary (for deserialization)."""
        # This will be implemented in concrete classes
        raise NotImplementedError("Subclasses must implement from_dict method")
    
    def __str__(self) -> str:
        """String representation of the user."""
        return f"{self.get_role()}: {self.full_name} (@{self.username})"
    
    def __repr__(self) -> str:
        """Developer representation of the user."""
        return f"<{self.__class__.__name__}(id={self.user_id}, username={self.username})>"
