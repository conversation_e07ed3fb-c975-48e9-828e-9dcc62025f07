"""
Utility functions for displaying time slots in pretty tables.
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any
from models.time_slot import TimeSlot
from models.slot_manager import Slot<PERSON>anager

try:
    from prettytable import PrettyTable
    PRETTYTABLE_AVAILABLE = True
except ImportError:
    PRETTYTABLE_AVAILABLE = False
    print("PrettyTable not available. Install with: pip install prettytable")


class SlotDisplayManager:
    """Manages the display of time slots in various formats."""
    
    def __init__(self, slot_manager: SlotManager):
        """
        Initialize the display manager.
        
        Args:
            slot_manager (SlotManager): The slot manager instance
        """
        self.slot_manager = slot_manager
    
    def display_weekly_slots(self, slots: List[TimeSlot], title: str = "Available Slots") -> str:
        """
        Display slots in a weekly grid format.
        
        Args:
            slots (List[TimeSlot]): List of slots to display
            title (str): Title for the table
            
        Returns:
            str: Formatted table string
        """
        if not PRETTYTABLE_AVAILABLE:
            return self._display_simple_list(slots, title)
        
        # Get next 5 working days
        working_days = self.slot_manager.get_next_working_days(5)
        
        # Create table
        table = PrettyTable()
        table.title = title
        
        # Set up columns: Time + 5 working days
        columns = ["Time"] + [day.strftime("%a %m/%d") for day in working_days]
        table.field_names = columns
        
        # Get all possible time slots for a day
        sample_day = working_days[0] if working_days else datetime.now()
        time_slots = self.slot_manager.get_working_hours_slots(sample_day)
        
        # Create a mapping of (date, time) -> slot
        slot_map = {}
        for slot in slots:
            date_key = slot.start_time.date()
            time_key = slot.start_time.time()
            slot_map[(date_key, time_key)] = slot
        
        # Build table rows
        for time_slot in time_slots:
            row = [time_slot.strftime("%H:%M")]
            
            for day in working_days:
                day_date = day.date()
                time_key = time_slot.time()
                
                if (day_date, time_key) in slot_map:
                    slot = slot_map[(day_date, time_key)]
                    cell = self._format_slot_cell(slot)
                else:
                    cell = "---"
                
                row.append(cell)
            
            table.add_row(row)
        
        # Style the table
        table.align = "c"
        table.border = True
        table.header = True
        table.padding_width = 1
        
        return str(table)
    
    def display_slot_list(self, slots: List[TimeSlot], title: str = "Slots") -> str:
        """
        Display slots in a list format.
        
        Args:
            slots (List[TimeSlot]): List of slots to display
            title (str): Title for the table
            
        Returns:
            str: Formatted table string
        """
        if not PRETTYTABLE_AVAILABLE:
            return self._display_simple_list(slots, title)
        
        table = PrettyTable()
        table.title = title
        table.field_names = ["#", "Date", "Time", "Volunteer", "Topic", "Status", "Student"]
        
        for i, slot in enumerate(slots, 1):
            # Get volunteer name (this would need to be passed or looked up)
            volunteer_name = f"Vol-{slot.volunteer_id[:8]}"
            student_name = f"Stu-{slot.student_id[:8]}" if slot.student_id else "---"
            
            table.add_row([
                i,
                slot.get_formatted_date(),
                slot.get_formatted_time(),
                volunteer_name,
                slot.topic or "General Help",
                self._get_status_emoji(slot.status),
                student_name
            ])
        
        table.align = "l"
        table.align["#"] = "r"
        table.align["Time"] = "c"
        table.align["Status"] = "c"
        
        return str(table)
    
    def display_volunteer_schedule(self, volunteer_slots: List[TimeSlot], 
                                 volunteer_name: str = "Volunteer") -> str:
        """
        Display a volunteer's schedule.
        
        Args:
            volunteer_slots (List[TimeSlot]): Volunteer's slots
            volunteer_name (str): Volunteer's name
            
        Returns:
            str: Formatted schedule string
        """
        if not PRETTYTABLE_AVAILABLE:
            return self._display_simple_list(volunteer_slots, f"{volunteer_name}'s Schedule")
        
        table = PrettyTable()
        table.title = f"{volunteer_name}'s Schedule"
        table.field_names = ["Date", "Time", "Topic", "Status", "Student", "Rating"]
        
        for slot in volunteer_slots:
            student_info = f"Stu-{slot.student_id[:8]}" if slot.student_id else "---"
            rating = f"⭐{slot.student_rating}" if slot.student_rating else "---"
            
            table.add_row([
                slot.get_formatted_date(),
                slot.get_formatted_time(),
                slot.topic or "General Help",
                self._get_status_emoji(slot.status),
                student_info,
                rating
            ])
        
        table.align = "l"
        table.align["Time"] = "c"
        table.align["Status"] = "c"
        table.align["Rating"] = "c"
        
        return str(table)
    
    def display_student_bookings(self, student_bookings: List[TimeSlot], 
                               student_name: str = "Student") -> str:
        """
        Display a student's bookings.
        
        Args:
            student_bookings (List[TimeSlot]): Student's bookings
            student_name (str): Student's name
            
        Returns:
            str: Formatted bookings string
        """
        if not PRETTYTABLE_AVAILABLE:
            return self._display_simple_list(student_bookings, f"{student_name}'s Bookings")
        
        table = PrettyTable()
        table.title = f"{student_name}'s Bookings"
        table.field_names = ["Date", "Time", "Volunteer", "Topic", "Status", "My Rating"]
        
        for slot in student_bookings:
            volunteer_info = f"Vol-{slot.volunteer_id[:8]}"
            rating = f"⭐{slot.student_rating}" if slot.student_rating else "---"
            
            table.add_row([
                slot.get_formatted_date(),
                slot.get_formatted_time(),
                volunteer_info,
                slot.topic or "General Help",
                self._get_status_emoji(slot.status),
                rating
            ])
        
        table.align = "l"
        table.align["Time"] = "c"
        table.align["Status"] = "c"
        table.align["My Rating"] = "c"
        
        return str(table)
    
    def display_daily_summary(self, date: datetime, slots: List[TimeSlot]) -> str:
        """
        Display a summary for a specific day.
        
        Args:
            date (datetime): The date to display
            slots (List[TimeSlot]): Slots for that day
            
        Returns:
            str: Formatted summary string
        """
        day_slots = [s for s in slots if s.start_time.date() == date.date()]
        
        if not PRETTYTABLE_AVAILABLE:
            return self._display_simple_list(day_slots, f"Slots for {date.strftime('%A, %B %d')}")
        
        table = PrettyTable()
        table.title = f"Slots for {date.strftime('%A, %B %d, %Y')}"
        table.field_names = ["Time", "Volunteer", "Topic", "Status", "Student"]
        
        for slot in day_slots:
            volunteer_info = f"Vol-{slot.volunteer_id[:8]}"
            student_info = f"Stu-{slot.student_id[:8]}" if slot.student_id else "---"
            
            table.add_row([
                slot.get_formatted_time(),
                volunteer_info,
                slot.topic or "General Help",
                self._get_status_emoji(slot.status),
                student_info
            ])
        
        table.align = "l"
        table.align["Time"] = "c"
        table.align["Status"] = "c"
        
        return str(table)
    
    def _format_slot_cell(self, slot: TimeSlot) -> str:
        """
        Format a slot for display in a table cell.
        
        Args:
            slot (TimeSlot): The slot to format
            
        Returns:
            str: Formatted cell content
        """
        status_emoji = self._get_status_emoji(slot.status)
        topic = slot.topic[:8] + "..." if len(slot.topic) > 8 else slot.topic
        return f"{status_emoji}\n{topic or 'Help'}"
    
    def _get_status_emoji(self, status: str) -> str:
        """
        Get emoji for slot status.
        
        Args:
            status (str): Slot status
            
        Returns:
            str: Status emoji
        """
        status_emojis = {
            'available': '🟢',
            'booked': '🔴',
            'completed': '✅',
            'cancelled': '❌'
        }
        return status_emojis.get(status, '⚪')
    
    def _display_simple_list(self, slots: List[TimeSlot], title: str) -> str:
        """
        Display slots in a simple text list format (fallback when PrettyTable not available).
        
        Args:
            slots (List[TimeSlot]): List of slots to display
            title (str): Title for the list
            
        Returns:
            str: Simple formatted list
        """
        output = [f"\n{title}", "=" * len(title)]
        
        if not slots:
            output.append("No slots available.")
            return "\n".join(output)
        
        for i, slot in enumerate(slots, 1):
            status_emoji = self._get_status_emoji(slot.status)
            student_info = f" (Student: {slot.student_id[:8]})" if slot.student_id else ""
            
            output.append(
                f"{i:2d}. {status_emoji} {slot.get_formatted_date()} {slot.get_formatted_time()} "
                f"- {slot.topic or 'General Help'}{student_info}"
            )
        
        return "\n".join(output)
