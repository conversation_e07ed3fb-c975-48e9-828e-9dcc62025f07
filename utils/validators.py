"""
Input validation utilities for the Code Clinics application.
"""

import re
from typing import Tuple


class InputValidator:
    """Provides validation methods for user input."""
    
    @staticmethod
    def validate_email(email: str) -> Tuple[bool, str]:
        """
        Validate email address format and domain restrictions.

        Args:
            email (str): Email address to validate

        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        if not email:
            return False, "Email cannot be empty"

        # Basic email regex pattern
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

        if not re.match(pattern, email):
            return False, "Invalid email format"

        # Check for WeThinkCode domain restrictions
        allowed_domains = ['student.wethinkcode.co.za', 'wethinkcode.co.za']
        admin_email = '<EMAIL>'

        if email == admin_email:
            return True, ""

        for domain in allowed_domains:
            if email.endswith(f'@{domain}'):
                return True, ""

        return False, "Email must be from WeThinkCode domain (@student.wethinkcode.co.za or @wethinkcode.co.za)"
    
    @staticmethod
    def validate_username(username: str) -> Tuple[bool, str]:
        """
        Validate username format.
        
        Args:
            username (str): Username to validate
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        if not username:
            return False, "Username cannot be empty"
        
        if len(username) < 3:
            return False, "Username must be at least 3 characters long"
        
        if len(username) > 20:
            return False, "Username must be no more than 20 characters long"
        
        # Allow alphanumeric characters, underscores, and hyphens
        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            return False, "Username can only contain letters, numbers, underscores, and hyphens"
        
        return True, ""
    
    @staticmethod
    def validate_rating(rating_str: str) -> Tuple[bool, str]:
        """
        Validate rating input.
        
        Args:
            rating_str (str): Rating as string
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        try:
            rating = int(rating_str)
            if 1 <= rating <= 5:
                return True, ""
            else:
                return False, "Rating must be between 1 and 5"
        except ValueError:
            return False, "Rating must be a number"
    
    @staticmethod
    def validate_non_empty(text: str, field_name: str = "Field") -> Tuple[bool, str]:
        """
        Validate that text is not empty.
        
        Args:
            text (str): Text to validate
            field_name (str): Name of the field for error message
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        if not text or not text.strip():
            return False, f"{field_name} cannot be empty"
        return True, ""
    
    @staticmethod
    def validate_choice(choice: str, min_val: int, max_val: int) -> Tuple[bool, str]:
        """
        Validate menu choice input.
        
        Args:
            choice (str): Choice as string
            min_val (int): Minimum valid value
            max_val (int): Maximum valid value
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        try:
            choice_int = int(choice)
            if min_val <= choice_int <= max_val:
                return True, ""
            else:
                return False, f"Choice must be between {min_val} and {max_val}"
        except ValueError:
            return False, "Please enter a valid number"
