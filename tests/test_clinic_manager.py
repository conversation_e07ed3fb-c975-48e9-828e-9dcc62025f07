"""
Unit tests for ClinicManager class.
"""

import unittest
import sys
import os
import tempfile
import shutil
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.clinic_manager import ClinicManager
from models.student import Student
from models.volunteer import Volunteer


class TestClinicManager(unittest.TestCase):
    """Test cases for the ClinicManager class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a temporary directory for test data
        self.test_data_dir = tempfile.mkdtemp()
        self.clinic_manager = ClinicManager(data_dir=self.test_data_dir)
    
    def tearDown(self):
        """Clean up after each test method."""
        # Remove the temporary directory
        shutil.rmtree(self.test_data_dir)
    
    def test_clinic_manager_creation(self):
        """Test clinic manager object creation."""
        self.assertIsNotNone(self.clinic_manager)
        self.assertEqual(len(self.clinic_manager.users), 0)
        self.assertEqual(len(self.clinic_manager.sessions), 0)
        self.assertEqual(len(self.clinic_manager.help_requests), 0)
        self.assertIsNone(self.clinic_manager.current_user)
        self.assertTrue(os.path.exists(self.test_data_dir))
    
    def test_register_student(self):
        """Test registering a student."""
        result = self.clinic_manager.register_user(
            username="test_student",
            email="<EMAIL>",
            full_name="Test Student",
            role="student",
            skill_level="beginner",
            learning_goals=["Python", "OOP"]
        )
        
        self.assertTrue(result)
        self.assertEqual(len(self.clinic_manager.users), 1)
        
        # Get the registered user
        user = list(self.clinic_manager.users.values())[0]
        self.assertIsInstance(user, Student)
        self.assertEqual(user.username, "test_student")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.skill_level, "beginner")
        self.assertEqual(user.learning_goals, ["Python", "OOP"])
    
    def test_register_volunteer(self):
        """Test registering a volunteer."""
        result = self.clinic_manager.register_user(
            username="test_volunteer",
            email="<EMAIL>",
            full_name="Test Volunteer",
            role="volunteer",
            experience_level="advanced",
            expertise_areas=["Python", "Web Development"]
        )
        
        self.assertTrue(result)
        self.assertEqual(len(self.clinic_manager.users), 1)
        
        # Get the registered user
        user = list(self.clinic_manager.users.values())[0]
        self.assertIsInstance(user, Volunteer)
        self.assertEqual(user.username, "test_volunteer")
        self.assertEqual(user.email, "<EMAIL>")
        self.assertEqual(user.experience_level, "advanced")
        self.assertEqual(user.expertise_areas, ["Python", "Web Development"])
    
    def test_register_duplicate_username(self):
        """Test registering users with duplicate usernames."""
        # Register first user
        result1 = self.clinic_manager.register_user(
            username="duplicate_user",
            email="<EMAIL>",
            full_name="User One",
            role="student"
        )
        self.assertTrue(result1)
        
        # Try to register second user with same username
        result2 = self.clinic_manager.register_user(
            username="duplicate_user",
            email="<EMAIL>",
            full_name="User Two",
            role="volunteer"
        )
        self.assertFalse(result2)
        self.assertEqual(len(self.clinic_manager.users), 1)
    
    def test_register_invalid_role(self):
        """Test registering user with invalid role."""
        result = self.clinic_manager.register_user(
            username="invalid_user",
            email="<EMAIL>",
            full_name="Invalid User",
            role="admin"  # Invalid role
        )
        
        self.assertFalse(result)
        self.assertEqual(len(self.clinic_manager.users), 0)
    
    def test_login_user(self):
        """Test user login."""
        # Register a user first
        self.clinic_manager.register_user(
            username="login_test",
            email="<EMAIL>",
            full_name="Login Test",
            role="student"
        )
        
        # Test successful login
        result = self.clinic_manager.login_user("login_test")
        self.assertTrue(result)
        self.assertIsNotNone(self.clinic_manager.current_user)
        self.assertEqual(self.clinic_manager.current_user.username, "login_test")
        
        # Test login with non-existent username
        result = self.clinic_manager.login_user("non_existent")
        self.assertFalse(result)
        # Current user should remain the same
        self.assertEqual(self.clinic_manager.current_user.username, "login_test")
    
    def test_login_inactive_user(self):
        """Test login with inactive user."""
        # Register and deactivate a user
        self.clinic_manager.register_user(
            username="inactive_user",
            email="<EMAIL>",
            full_name="Inactive User",
            role="student"
        )
        
        user = self.clinic_manager.get_user_by_username("inactive_user")
        user.deactivate()
        
        # Try to login with inactive user
        result = self.clinic_manager.login_user("inactive_user")
        self.assertFalse(result)
        self.assertIsNone(self.clinic_manager.current_user)
    
    def test_logout_user(self):
        """Test user logout."""
        # Register and login a user
        self.clinic_manager.register_user(
            username="logout_test",
            email="<EMAIL>",
            full_name="Logout Test",
            role="student"
        )
        self.clinic_manager.login_user("logout_test")
        
        # Logout
        self.clinic_manager.logout_user()
        self.assertIsNone(self.clinic_manager.current_user)
    
    def test_get_user_by_username(self):
        """Test getting user by username."""
        # Register a user
        self.clinic_manager.register_user(
            username="find_me",
            email="<EMAIL>",
            full_name="Find Me",
            role="student"
        )
        
        # Test finding existing user
        user = self.clinic_manager.get_user_by_username("find_me")
        self.assertIsNotNone(user)
        self.assertEqual(user.username, "find_me")
        
        # Test finding non-existent user
        user = self.clinic_manager.get_user_by_username("not_found")
        self.assertIsNone(user)
    
    def test_get_students_and_volunteers(self):
        """Test getting students and volunteers separately."""
        # Register students and volunteers
        self.clinic_manager.register_user(
            username="student1", email="<EMAIL>",
            full_name="Student One", role="student"
        )
        self.clinic_manager.register_user(
            username="student2", email="<EMAIL>",
            full_name="Student Two", role="student"
        )
        self.clinic_manager.register_user(
            username="volunteer1", email="<EMAIL>",
            full_name="Volunteer One", role="volunteer"
        )
        
        students = self.clinic_manager.get_students()
        volunteers = self.clinic_manager.get_volunteers()
        
        self.assertEqual(len(students), 2)
        self.assertEqual(len(volunteers), 1)
        self.assertTrue(all(isinstance(s, Student) for s in students))
        self.assertTrue(all(isinstance(v, Volunteer) for v in volunteers))
    
    def test_get_available_volunteers(self):
        """Test getting available volunteers."""
        # Register volunteers
        self.clinic_manager.register_user(
            username="available_vol", email="<EMAIL>",
            full_name="Available Volunteer", role="volunteer"
        )
        self.clinic_manager.register_user(
            username="unavailable_vol", email="<EMAIL>",
            full_name="Unavailable Volunteer", role="volunteer"
        )
        
        # Set one volunteer as unavailable
        unavailable_vol = self.clinic_manager.get_user_by_username("unavailable_vol")
        unavailable_vol.set_availability(False)
        
        available_volunteers = self.clinic_manager.get_available_volunteers()
        self.assertEqual(len(available_volunteers), 1)
        self.assertEqual(available_volunteers[0].username, "available_vol")
    
    def test_create_help_request(self):
        """Test creating help requests."""
        # Register and login a student
        self.clinic_manager.register_user(
            username="help_student", email="<EMAIL>",
            full_name="Help Student", role="student"
        )
        self.clinic_manager.login_user("help_student")
        
        # Create help request
        request_id = self.clinic_manager.create_help_request(
            topic="Python Basics",
            description="Need help with loops",
            urgency="normal"
        )
        
        self.assertIsNotNone(request_id)
        self.assertIn(request_id, self.clinic_manager.help_requests)
        
        help_request = self.clinic_manager.help_requests[request_id]
        self.assertEqual(help_request['topic'], "Python Basics")
        self.assertEqual(help_request['description'], "Need help with loops")
        self.assertEqual(help_request['urgency'], "normal")
        self.assertEqual(help_request['status'], 'open')
    
    def test_create_help_request_non_student(self):
        """Test creating help request as non-student."""
        # Register and login a volunteer
        self.clinic_manager.register_user(
            username="vol_user", email="<EMAIL>",
            full_name="Volunteer User", role="volunteer"
        )
        self.clinic_manager.login_user("vol_user")
        
        # Try to create help request
        request_id = self.clinic_manager.create_help_request(
            topic="Test Topic",
            description="Test Description"
        )
        
        self.assertIsNone(request_id)
        self.assertEqual(len(self.clinic_manager.help_requests), 0)
    
    def test_get_open_help_requests(self):
        """Test getting open help requests."""
        # Register and login a student
        self.clinic_manager.register_user(
            username="req_student", email="<EMAIL>",
            full_name="Request Student", role="student"
        )
        self.clinic_manager.login_user("req_student")
        
        # Create multiple help requests
        request_id1 = self.clinic_manager.create_help_request("Topic 1", "Description 1")
        request_id2 = self.clinic_manager.create_help_request("Topic 2", "Description 2")
        
        # Mark one as accepted
        self.clinic_manager.help_requests[request_id1]['status'] = 'accepted'
        
        open_requests = self.clinic_manager.get_open_help_requests()
        self.assertEqual(len(open_requests), 1)
        self.assertEqual(open_requests[0]['request_id'], request_id2)
    
    def test_accept_help_request(self):
        """Test accepting help requests."""
        # Register student and volunteer
        self.clinic_manager.register_user(
            username="help_student", email="<EMAIL>",
            full_name="Help Student", role="student"
        )
        self.clinic_manager.register_user(
            username="help_volunteer", email="<EMAIL>",
            full_name="Help Volunteer", role="volunteer"
        )
        
        # Student creates help request
        self.clinic_manager.login_user("help_student")
        request_id = self.clinic_manager.create_help_request("Python Help", "Need assistance")
        
        # Volunteer accepts request
        self.clinic_manager.logout_user()
        self.clinic_manager.login_user("help_volunteer")
        session_id = self.clinic_manager.accept_help_request(request_id)
        
        self.assertIsNotNone(session_id)
        self.assertIn(session_id, self.clinic_manager.sessions)
        
        # Check help request status
        help_request = self.clinic_manager.help_requests[request_id]
        self.assertEqual(help_request['status'], 'accepted')
        self.assertEqual(help_request['session_id'], session_id)
        
        # Check session details
        session = self.clinic_manager.sessions[session_id]
        self.assertEqual(session.topic, "Python Help")
        self.assertEqual(session.description, "Need assistance")
        self.assertEqual(session.status, "scheduled")
    
    def test_accept_help_request_non_volunteer(self):
        """Test accepting help request as non-volunteer."""
        # Register and login a student
        self.clinic_manager.register_user(
            username="student_user", email="<EMAIL>",
            full_name="Student User", role="student"
        )
        self.clinic_manager.login_user("student_user")
        
        # Try to accept help request
        session_id = self.clinic_manager.accept_help_request("fake_request_id")
        self.assertIsNone(session_id)
    
    def test_get_user_sessions(self):
        """Test getting user sessions."""
        # Set up users and session
        self.clinic_manager.register_user(
            username="session_student", email="<EMAIL>",
            full_name="Session Student", role="student"
        )
        self.clinic_manager.register_user(
            username="session_volunteer", email="<EMAIL>",
            full_name="Session Volunteer", role="volunteer"
        )
        
        # Create session through help request
        self.clinic_manager.login_user("session_student")
        request_id = self.clinic_manager.create_help_request("Test Topic", "Test Description")
        
        self.clinic_manager.logout_user()
        self.clinic_manager.login_user("session_volunteer")
        session_id = self.clinic_manager.accept_help_request(request_id)
        
        # Test getting sessions for volunteer
        volunteer_sessions = self.clinic_manager.get_user_sessions()
        self.assertEqual(len(volunteer_sessions), 1)
        self.assertEqual(volunteer_sessions[0].session_id, session_id)
        
        # Test getting sessions for student
        self.clinic_manager.logout_user()
        self.clinic_manager.login_user("session_student")
        student_sessions = self.clinic_manager.get_user_sessions()
        self.assertEqual(len(student_sessions), 1)
        self.assertEqual(student_sessions[0].session_id, session_id)
    
    def test_start_and_end_session(self):
        """Test starting and ending sessions."""
        # Set up session
        self.clinic_manager.register_user(
            username="test_student", email="<EMAIL>",
            full_name="Test Student", role="student"
        )
        self.clinic_manager.register_user(
            username="test_volunteer", email="<EMAIL>",
            full_name="Test Volunteer", role="volunteer"
        )
        
        self.clinic_manager.login_user("test_student")
        request_id = self.clinic_manager.create_help_request("Test", "Test")
        
        self.clinic_manager.logout_user()
        self.clinic_manager.login_user("test_volunteer")
        session_id = self.clinic_manager.accept_help_request(request_id)
        
        # Test starting session
        result = self.clinic_manager.start_session(session_id)
        self.assertTrue(result)
        
        session = self.clinic_manager.sessions[session_id]
        self.assertEqual(session.status, "in_progress")
        
        # Test ending session
        result = self.clinic_manager.end_session(session_id, "Session completed")
        self.assertTrue(result)
        self.assertEqual(session.status, "completed")
        self.assertEqual(session.notes, "Session completed")
    
    def test_rate_session(self):
        """Test rating sessions."""
        # Set up completed session
        self.clinic_manager.register_user(
            username="rate_student", email="<EMAIL>",
            full_name="Rate Student", role="student"
        )
        self.clinic_manager.register_user(
            username="rate_volunteer", email="<EMAIL>",
            full_name="Rate Volunteer", role="volunteer"
        )
        
        self.clinic_manager.login_user("rate_student")
        request_id = self.clinic_manager.create_help_request("Rate Test", "Rate Test")
        
        self.clinic_manager.logout_user()
        self.clinic_manager.login_user("rate_volunteer")
        session_id = self.clinic_manager.accept_help_request(request_id)
        
        self.clinic_manager.start_session(session_id)
        self.clinic_manager.end_session(session_id, "Done")
        
        # Test volunteer rating student
        result = self.clinic_manager.rate_session(session_id, 4, "Good student")
        self.assertTrue(result)
        
        session = self.clinic_manager.sessions[session_id]
        self.assertEqual(session.volunteer_rating, 4)
        self.assertEqual(session.volunteer_feedback, "Good student")
        
        # Test student rating volunteer
        self.clinic_manager.logout_user()
        self.clinic_manager.login_user("rate_student")
        result = self.clinic_manager.rate_session(session_id, 5, "Excellent help")
        self.assertTrue(result)
        
        self.assertEqual(session.student_rating, 5)
        self.assertEqual(session.student_feedback, "Excellent help")
        
        # Check volunteer's rating was updated
        volunteer = self.clinic_manager.get_user_by_username("rate_volunteer")
        self.assertEqual(volunteer.rating, 5.0)
        self.assertEqual(volunteer.total_ratings, 1)
    
    def test_find_volunteers_for_topic(self):
        """Test finding volunteers for specific topics."""
        # Register volunteers with different expertise
        self.clinic_manager.register_user(
            username="python_vol", email="<EMAIL>",
            full_name="Python Volunteer", role="volunteer",
            expertise_areas=["Python", "Django"]
        )
        self.clinic_manager.register_user(
            username="js_vol", email="<EMAIL>",
            full_name="JS Volunteer", role="volunteer",
            expertise_areas=["JavaScript", "React"]
        )
        
        # Add programming languages
        python_vol = self.clinic_manager.get_user_by_username("python_vol")
        python_vol.add_programming_language("Python")
        
        js_vol = self.clinic_manager.get_user_by_username("js_vol")
        js_vol.add_programming_language("JavaScript")
        
        # Test finding volunteers by topic
        python_volunteers = self.clinic_manager.find_volunteers_for_topic("Python programming")
        self.assertEqual(len(python_volunteers), 1)
        self.assertEqual(python_volunteers[0].username, "python_vol")
        
        # Test finding volunteers by topic and language
        python_lang_volunteers = self.clinic_manager.find_volunteers_for_topic("programming", "Python")
        self.assertEqual(len(python_lang_volunteers), 1)
        self.assertEqual(python_lang_volunteers[0].username, "python_vol")
        
        # Test finding volunteers for non-matching topic
        ml_volunteers = self.clinic_manager.find_volunteers_for_topic("Machine Learning")
        self.assertEqual(len(ml_volunteers), 0)
    
    def test_data_persistence(self):
        """Test data saving and loading."""
        # Register some users
        self.clinic_manager.register_user(
            username="persist_student", email="<EMAIL>",
            full_name="Persist Student", role="student"
        )
        self.clinic_manager.register_user(
            username="persist_volunteer", email="<EMAIL>",
            full_name="Persist Volunteer", role="volunteer"
        )
        
        # Create a session
        self.clinic_manager.login_user("persist_student")
        request_id = self.clinic_manager.create_help_request("Persist Test", "Persist Test")
        
        self.clinic_manager.logout_user()
        self.clinic_manager.login_user("persist_volunteer")
        session_id = self.clinic_manager.accept_help_request(request_id)
        
        # Save data
        self.clinic_manager.save_data()
        
        # Create new clinic manager with same data directory
        new_clinic_manager = ClinicManager(data_dir=self.test_data_dir)
        
        # Verify data was loaded
        self.assertEqual(len(new_clinic_manager.users), 2)
        self.assertEqual(len(new_clinic_manager.sessions), 1)
        self.assertEqual(len(new_clinic_manager.help_requests), 1)
        
        # Verify specific data
        student = new_clinic_manager.get_user_by_username("persist_student")
        self.assertIsNotNone(student)
        self.assertIsInstance(student, Student)
        
        volunteer = new_clinic_manager.get_user_by_username("persist_volunteer")
        self.assertIsNotNone(volunteer)
        self.assertIsInstance(volunteer, Volunteer)


if __name__ == '__main__':
    unittest.main()
