"""
Unit tests for User model classes (User, Student, Volunteer).
"""

import unittest
import sys
import os
from datetime import datetime

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.user import User
from models.student import Student
from models.volunteer import Volunteer


class TestStudent(unittest.TestCase):
    """Test cases for the Student class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.student = Student(
            username="test_student",
            email="<EMAIL>",
            full_name="Test Student",
            skill_level="beginner",
            learning_goals=["Python", "OOP"]
        )
    
    def test_student_creation(self):
        """Test student object creation."""
        self.assertEqual(self.student.username, "test_student")
        self.assertEqual(self.student.email, "<EMAIL>")
        self.assertEqual(self.student.full_name, "Test Student")
        self.assertEqual(self.student.skill_level, "beginner")
        self.assertEqual(self.student.learning_goals, ["Python", "OOP"])
        self.assertTrue(self.student.is_active)
        self.assertIsNotNone(self.student.user_id)
        self.assertIsInstance(self.student.created_at, datetime)
    
    def test_student_role(self):
        """Test student role identification."""
        self.assertEqual(self.student.get_role(), "Student")
    
    def test_student_capabilities(self):
        """Test student capabilities."""
        capabilities = self.student.get_capabilities()
        expected_capabilities = [
            "request_help",
            "join_session",
            "rate_volunteer",
            "view_session_history",
            "update_learning_goals"
        ]
        self.assertEqual(capabilities, expected_capabilities)
    
    def test_add_learning_goal(self):
        """Test adding learning goals."""
        initial_count = len(self.student.learning_goals)
        self.student.add_learning_goal("Web Development")
        self.assertEqual(len(self.student.learning_goals), initial_count + 1)
        self.assertIn("Web Development", self.student.learning_goals)
        
        # Test adding duplicate goal
        self.student.add_learning_goal("Python")
        self.assertEqual(len(self.student.learning_goals), initial_count + 1)  # Should not increase
    
    def test_remove_learning_goal(self):
        """Test removing learning goals."""
        initial_count = len(self.student.learning_goals)
        self.student.remove_learning_goal("Python")
        self.assertEqual(len(self.student.learning_goals), initial_count - 1)
        self.assertNotIn("Python", self.student.learning_goals)
        
        # Test removing non-existent goal
        self.student.remove_learning_goal("Non-existent")
        self.assertEqual(len(self.student.learning_goals), initial_count - 1)  # Should not change
    
    def test_add_preferred_language(self):
        """Test adding preferred programming languages."""
        self.student.add_preferred_language("JavaScript")
        self.assertIn("JavaScript", self.student.preferred_languages)
        
        # Test adding duplicate language
        self.student.add_preferred_language("JavaScript")
        self.assertEqual(self.student.preferred_languages.count("JavaScript"), 1)
    
    def test_request_help(self):
        """Test creating help requests."""
        request_id = self.student.request_help(
            topic="Python Basics",
            description="Need help with loops",
            urgency="normal"
        )
        
        self.assertIsNotNone(request_id)
        self.assertIn(request_id, self.student.help_requests)
    
    def test_complete_session(self):
        """Test completing sessions."""
        session_id = "test_session_123"
        self.student.complete_session(session_id)
        self.assertIn(session_id, self.student.completed_sessions)
        
        # Test adding duplicate session
        self.student.complete_session(session_id)
        self.assertEqual(self.student.completed_sessions.count(session_id), 1)
    
    def test_to_dict(self):
        """Test serialization to dictionary."""
        student_dict = self.student.to_dict()
        
        self.assertEqual(student_dict['username'], "test_student")
        self.assertEqual(student_dict['email'], "<EMAIL>")
        self.assertEqual(student_dict['role'], "Student")
        self.assertEqual(student_dict['skill_level'], "beginner")
        self.assertEqual(student_dict['learning_goals'], ["Python", "OOP"])
        self.assertIn('user_id', student_dict)
        self.assertIn('created_at', student_dict)
    
    def test_from_dict(self):
        """Test deserialization from dictionary."""
        student_dict = self.student.to_dict()
        restored_student = Student.from_dict(student_dict)
        
        self.assertEqual(restored_student.username, self.student.username)
        self.assertEqual(restored_student.email, self.student.email)
        self.assertEqual(restored_student.full_name, self.student.full_name)
        self.assertEqual(restored_student.skill_level, self.student.skill_level)
        self.assertEqual(restored_student.learning_goals, self.student.learning_goals)


class TestVolunteer(unittest.TestCase):
    """Test cases for the Volunteer class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.volunteer = Volunteer(
            username="test_volunteer",
            email="<EMAIL>",
            full_name="Test Volunteer",
            expertise_areas=["Python", "Web Development"],
            experience_level="advanced"
        )
    
    def test_volunteer_creation(self):
        """Test volunteer object creation."""
        self.assertEqual(self.volunteer.username, "test_volunteer")
        self.assertEqual(self.volunteer.email, "<EMAIL>")
        self.assertEqual(self.volunteer.full_name, "Test Volunteer")
        self.assertEqual(self.volunteer.expertise_areas, ["Python", "Web Development"])
        self.assertEqual(self.volunteer.experience_level, "advanced")
        self.assertTrue(self.volunteer.is_active)
        self.assertTrue(self.volunteer.is_available)
        self.assertEqual(self.volunteer.rating, 0.0)
        self.assertEqual(self.volunteer.total_ratings, 0)
    
    def test_volunteer_role(self):
        """Test volunteer role identification."""
        self.assertEqual(self.volunteer.get_role(), "Volunteer")
    
    def test_volunteer_capabilities(self):
        """Test volunteer capabilities."""
        capabilities = self.volunteer.get_capabilities()
        expected_capabilities = [
            "accept_help_requests",
            "conduct_sessions",
            "set_availability",
            "view_student_requests",
            "update_expertise",
            "view_session_history"
        ]
        self.assertEqual(capabilities, expected_capabilities)
    
    def test_add_expertise_area(self):
        """Test adding expertise areas."""
        initial_count = len(self.volunteer.expertise_areas)
        self.volunteer.add_expertise_area("Machine Learning")
        self.assertEqual(len(self.volunteer.expertise_areas), initial_count + 1)
        self.assertIn("Machine Learning", self.volunteer.expertise_areas)
        
        # Test adding duplicate area
        self.volunteer.add_expertise_area("Python")
        self.assertEqual(len(self.volunteer.expertise_areas), initial_count + 1)  # Should not increase
    
    def test_remove_expertise_area(self):
        """Test removing expertise areas."""
        initial_count = len(self.volunteer.expertise_areas)
        self.volunteer.remove_expertise_area("Python")
        self.assertEqual(len(self.volunteer.expertise_areas), initial_count - 1)
        self.assertNotIn("Python", self.volunteer.expertise_areas)
    
    def test_add_programming_language(self):
        """Test adding programming languages."""
        self.volunteer.add_programming_language("JavaScript")
        self.assertIn("JavaScript", self.volunteer.programming_languages)
        
        # Test adding duplicate language
        self.volunteer.add_programming_language("JavaScript")
        self.assertEqual(self.volunteer.programming_languages.count("JavaScript"), 1)
    
    def test_set_availability(self):
        """Test setting availability status."""
        self.volunteer.set_availability(False)
        self.assertFalse(self.volunteer.is_available)
        
        self.volunteer.set_availability(True)
        self.assertTrue(self.volunteer.is_available)
    
    def test_add_availability_hours(self):
        """Test adding availability hours."""
        hours = ["09:00-10:00", "14:00-15:00"]
        self.volunteer.add_availability_hours(hours)
        
        for hour in hours:
            self.assertIn(hour, self.volunteer.availability_hours)
        
        # Test adding duplicate hours
        self.volunteer.add_availability_hours(["09:00-10:00"])
        self.assertEqual(self.volunteer.availability_hours.count("09:00-10:00"), 1)
    
    def test_conduct_session(self):
        """Test conducting sessions."""
        session_id = "test_session_123"
        self.volunteer.conduct_session(session_id)
        self.assertIn(session_id, self.volunteer.sessions_conducted)
        
        # Test adding duplicate session
        self.volunteer.conduct_session(session_id)
        self.assertEqual(self.volunteer.sessions_conducted.count(session_id), 1)
    
    def test_add_rating(self):
        """Test adding ratings."""
        # Test valid ratings
        self.volunteer.add_rating(4.5)
        self.assertEqual(self.volunteer.rating, 4.5)
        self.assertEqual(self.volunteer.total_ratings, 1)
        
        self.volunteer.add_rating(3.5)
        self.assertEqual(self.volunteer.rating, 4.0)  # Average of 4.5 and 3.5
        self.assertEqual(self.volunteer.total_ratings, 2)
        
        # Test invalid ratings
        initial_rating = self.volunteer.rating
        initial_count = self.volunteer.total_ratings
        
        self.volunteer.add_rating(0.5)  # Too low
        self.assertEqual(self.volunteer.rating, initial_rating)
        self.assertEqual(self.volunteer.total_ratings, initial_count)
        
        self.volunteer.add_rating(5.5)  # Too high
        self.assertEqual(self.volunteer.rating, initial_rating)
        self.assertEqual(self.volunteer.total_ratings, initial_count)
    
    def test_can_help_with(self):
        """Test checking if volunteer can help with topics."""
        # Test topic matching
        self.assertTrue(self.volunteer.can_help_with("Python programming"))
        self.assertTrue(self.volunteer.can_help_with("web development"))
        self.assertFalse(self.volunteer.can_help_with("Machine Learning"))
        
        # Test with programming language
        self.volunteer.add_programming_language("JavaScript")
        self.assertTrue(self.volunteer.can_help_with("web development", "JavaScript"))
        self.assertFalse(self.volunteer.can_help_with("web development", "Go"))
    
    def test_to_dict(self):
        """Test serialization to dictionary."""
        volunteer_dict = self.volunteer.to_dict()
        
        self.assertEqual(volunteer_dict['username'], "test_volunteer")
        self.assertEqual(volunteer_dict['email'], "<EMAIL>")
        self.assertEqual(volunteer_dict['role'], "Volunteer")
        self.assertEqual(volunteer_dict['expertise_areas'], ["Python", "Web Development"])
        self.assertEqual(volunteer_dict['experience_level'], "advanced")
        self.assertIn('user_id', volunteer_dict)
        self.assertIn('created_at', volunteer_dict)
    
    def test_from_dict(self):
        """Test deserialization from dictionary."""
        volunteer_dict = self.volunteer.to_dict()
        restored_volunteer = Volunteer.from_dict(volunteer_dict)
        
        self.assertEqual(restored_volunteer.username, self.volunteer.username)
        self.assertEqual(restored_volunteer.email, self.volunteer.email)
        self.assertEqual(restored_volunteer.full_name, self.volunteer.full_name)
        self.assertEqual(restored_volunteer.expertise_areas, self.volunteer.expertise_areas)
        self.assertEqual(restored_volunteer.experience_level, self.volunteer.experience_level)


if __name__ == '__main__':
    unittest.main()
