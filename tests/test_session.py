"""
Unit tests for Session class.
"""

import unittest
import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.session import Session


class TestSession(unittest.TestCase):
    """Test cases for the Session class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.student_id = "student_123"
        self.volunteer_id = "volunteer_456"
        self.topic = "Python OOP"
        self.description = "Need help with classes and inheritance"
        self.scheduled_time = datetime.now() + timedelta(hours=1)
        
        self.session = Session(
            student_id=self.student_id,
            volunteer_id=self.volunteer_id,
            topic=self.topic,
            description=self.description,
            scheduled_time=self.scheduled_time
        )
    
    def test_session_creation(self):
        """Test session object creation."""
        self.assertEqual(self.session.student_id, self.student_id)
        self.assertEqual(self.session.volunteer_id, self.volunteer_id)
        self.assertEqual(self.session.topic, self.topic)
        self.assertEqual(self.session.description, self.description)
        self.assertEqual(self.session.scheduled_time, self.scheduled_time)
        self.assertEqual(self.session.status, "scheduled")
        self.assertEqual(self.session.difficulty_level, "medium")
        self.assertEqual(self.session.duration_minutes, 60)
        self.assertIsNotNone(self.session.session_id)
        self.assertIsInstance(self.session.created_at, datetime)
        self.assertIsNone(self.session.started_at)
        self.assertIsNone(self.session.ended_at)
        self.assertIsNone(self.session.student_rating)
        self.assertIsNone(self.session.volunteer_rating)
        self.assertEqual(self.session.notes, "")
        self.assertEqual(self.session.student_feedback, "")
        self.assertEqual(self.session.volunteer_feedback, "")
    
    def test_session_creation_with_default_time(self):
        """Test session creation with default scheduled time."""
        session = Session(
            student_id=self.student_id,
            volunteer_id=self.volunteer_id,
            topic=self.topic,
            description=self.description
        )
        
        # Should use current time as default
        time_diff = abs((session.scheduled_time - datetime.now()).total_seconds())
        self.assertLess(time_diff, 1)  # Should be within 1 second
    
    def test_start_session(self):
        """Test starting a session."""
        # Test starting a scheduled session
        result = self.session.start_session()
        self.assertTrue(result)
        self.assertEqual(self.session.status, "in_progress")
        self.assertIsNotNone(self.session.started_at)
        self.assertIsInstance(self.session.started_at, datetime)
        
        # Test starting an already started session
        result = self.session.start_session()
        self.assertFalse(result)
        self.assertEqual(self.session.status, "in_progress")  # Should remain the same
    
    def test_end_session(self):
        """Test ending a session."""
        # Start the session first
        self.session.start_session()
        
        # Test ending an in-progress session
        notes = "Session completed successfully"
        result = self.session.end_session(notes)
        self.assertTrue(result)
        self.assertEqual(self.session.status, "completed")
        self.assertEqual(self.session.notes, notes)
        self.assertIsNotNone(self.session.ended_at)
        self.assertIsInstance(self.session.ended_at, datetime)
        
        # Test ending an already completed session
        result = self.session.end_session("Additional notes")
        self.assertFalse(result)
        self.assertEqual(self.session.notes, notes)  # Should remain the same
    
    def test_end_session_without_starting(self):
        """Test ending a session that hasn't been started."""
        result = self.session.end_session("Notes")
        self.assertFalse(result)
        self.assertEqual(self.session.status, "scheduled")
        self.assertEqual(self.session.notes, "")
    
    def test_cancel_session(self):
        """Test cancelling a session."""
        # Test cancelling a scheduled session
        reason = "Student unavailable"
        result = self.session.cancel_session(reason)
        self.assertTrue(result)
        self.assertEqual(self.session.status, "cancelled")
        self.assertEqual(self.session.notes, f"Cancelled: {reason}")
        
        # Test cancelling an in-progress session
        session2 = Session(self.student_id, self.volunteer_id, self.topic, self.description)
        session2.start_session()
        result = session2.cancel_session("Technical issues")
        self.assertTrue(result)
        self.assertEqual(session2.status, "cancelled")
        
        # Test cancelling an already completed session
        session3 = Session(self.student_id, self.volunteer_id, self.topic, self.description)
        session3.start_session()
        session3.end_session("Completed")
        result = session3.cancel_session("Too late")
        self.assertFalse(result)
        self.assertEqual(session3.status, "completed")  # Should remain completed
    
    def test_cancel_session_without_reason(self):
        """Test cancelling a session without providing a reason."""
        result = self.session.cancel_session()
        self.assertTrue(result)
        self.assertEqual(self.session.status, "cancelled")
        self.assertEqual(self.session.notes, "Cancelled")
    
    def test_add_student_rating(self):
        """Test adding student rating."""
        # Test valid ratings
        result = self.session.add_student_rating(4, "Great help!")
        self.assertTrue(result)
        self.assertEqual(self.session.student_rating, 4)
        self.assertEqual(self.session.student_feedback, "Great help!")
        
        # Test invalid ratings
        result = self.session.add_student_rating(0, "Too low")
        self.assertFalse(result)
        self.assertEqual(self.session.student_rating, 4)  # Should remain unchanged
        
        result = self.session.add_student_rating(6, "Too high")
        self.assertFalse(result)
        self.assertEqual(self.session.student_rating, 4)  # Should remain unchanged
        
        # Test rating without feedback
        session2 = Session(self.student_id, self.volunteer_id, self.topic, self.description)
        result = session2.add_student_rating(5)
        self.assertTrue(result)
        self.assertEqual(session2.student_rating, 5)
        self.assertEqual(session2.student_feedback, "")
    
    def test_add_volunteer_rating(self):
        """Test adding volunteer rating."""
        # Test valid ratings
        result = self.session.add_volunteer_rating(3, "Student was prepared")
        self.assertTrue(result)
        self.assertEqual(self.session.volunteer_rating, 3)
        self.assertEqual(self.session.volunteer_feedback, "Student was prepared")
        
        # Test invalid ratings
        result = self.session.add_volunteer_rating(0, "Too low")
        self.assertFalse(result)
        self.assertEqual(self.session.volunteer_rating, 3)  # Should remain unchanged
        
        result = self.session.add_volunteer_rating(6, "Too high")
        self.assertFalse(result)
        self.assertEqual(self.session.volunteer_rating, 3)  # Should remain unchanged
    
    def test_get_duration(self):
        """Test getting session duration."""
        # Test duration when session hasn't started
        duration = self.session.get_duration()
        self.assertIsNone(duration)
        
        # Test duration when session is in progress
        self.session.start_session()
        duration = self.session.get_duration()
        self.assertIsNone(duration)  # No end time yet
        
        # Test duration when session is completed
        self.session.end_session("Completed")
        duration = self.session.get_duration()
        self.assertIsNotNone(duration)
        self.assertIsInstance(duration, timedelta)
        self.assertGreater(duration.total_seconds(), 0)
    
    def test_status_check_methods(self):
        """Test status checking methods."""
        # Test scheduled status
        self.assertTrue(self.session.is_scheduled())
        self.assertFalse(self.session.is_active())
        self.assertFalse(self.session.is_completed())
        
        # Test in_progress status
        self.session.start_session()
        self.assertFalse(self.session.is_scheduled())
        self.assertTrue(self.session.is_active())
        self.assertFalse(self.session.is_completed())
        
        # Test completed status
        self.session.end_session("Done")
        self.assertFalse(self.session.is_scheduled())
        self.assertFalse(self.session.is_active())
        self.assertTrue(self.session.is_completed())
    
    def test_set_programming_language(self):
        """Test setting programming language."""
        language = "Python"
        self.session.set_programming_language(language)
        self.assertEqual(self.session.programming_language, language)
    
    def test_set_difficulty_level(self):
        """Test setting difficulty level."""
        # Test valid difficulty levels
        for level in ["easy", "medium", "hard"]:
            self.session.set_difficulty_level(level)
            self.assertEqual(self.session.difficulty_level, level)
        
        # Test invalid difficulty level
        original_level = self.session.difficulty_level
        self.session.set_difficulty_level("impossible")
        self.assertEqual(self.session.difficulty_level, original_level)  # Should remain unchanged
    
    def test_set_calendar_event_id(self):
        """Test setting calendar event ID."""
        event_id = "calendar_event_123"
        self.session.set_calendar_event_id(event_id)
        self.assertEqual(self.session.calendar_event_id, event_id)
    
    def test_set_duration(self):
        """Test setting session duration."""
        # Test valid duration
        duration = 90
        self.session.set_duration(duration)
        self.assertEqual(self.session.duration_minutes, duration)
        
        # Test invalid duration
        original_duration = self.session.duration_minutes
        self.session.set_duration(0)
        self.assertEqual(self.session.duration_minutes, original_duration)  # Should remain unchanged
        
        self.session.set_duration(-30)
        self.assertEqual(self.session.duration_minutes, original_duration)  # Should remain unchanged
    
    def test_to_dict(self):
        """Test serialization to dictionary."""
        session_dict = self.session.to_dict()
        
        self.assertEqual(session_dict['student_id'], self.student_id)
        self.assertEqual(session_dict['volunteer_id'], self.volunteer_id)
        self.assertEqual(session_dict['topic'], self.topic)
        self.assertEqual(session_dict['description'], self.description)
        self.assertEqual(session_dict['status'], "scheduled")
        self.assertEqual(session_dict['difficulty_level'], "medium")
        self.assertEqual(session_dict['duration_minutes'], 60)
        self.assertIn('session_id', session_dict)
        self.assertIn('created_at', session_dict)
        self.assertIn('scheduled_time', session_dict)
    
    def test_from_dict(self):
        """Test deserialization from dictionary."""
        # Add some data to the session
        self.session.start_session()
        self.session.end_session("Test completed")
        self.session.add_student_rating(5, "Excellent")
        self.session.set_programming_language("Python")
        self.session.set_calendar_event_id("event_123")
        
        session_dict = self.session.to_dict()
        restored_session = Session.from_dict(session_dict)
        
        self.assertEqual(restored_session.student_id, self.session.student_id)
        self.assertEqual(restored_session.volunteer_id, self.session.volunteer_id)
        self.assertEqual(restored_session.topic, self.session.topic)
        self.assertEqual(restored_session.description, self.session.description)
        self.assertEqual(restored_session.status, self.session.status)
        self.assertEqual(restored_session.notes, self.session.notes)
        self.assertEqual(restored_session.student_rating, self.session.student_rating)
        self.assertEqual(restored_session.student_feedback, self.session.student_feedback)
        self.assertEqual(restored_session.programming_language, self.session.programming_language)
        self.assertEqual(restored_session.calendar_event_id, self.session.calendar_event_id)
        self.assertEqual(restored_session.duration_minutes, self.session.duration_minutes)
    
    def test_string_representations(self):
        """Test string representations of the session."""
        session_str = str(self.session)
        self.assertIn(self.topic, session_str)
        self.assertIn("scheduled", session_str)
        
        session_repr = repr(self.session)
        self.assertIn("Session", session_repr)
        self.assertIn(self.session.session_id, session_repr)
        self.assertIn(self.topic, session_repr)


if __name__ == '__main__':
    unittest.main()
