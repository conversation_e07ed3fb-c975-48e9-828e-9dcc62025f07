#!/usr/bin/env python3
"""
Test runner for the Code Clinics application.
"""

import unittest
import sys
import os
from io import StringIO

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def run_all_tests():
    """Run all tests and return results."""
    # Discover and run all tests
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(os.path.abspath(__file__))
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # Run tests with detailed output
    stream = StringIO()
    runner = unittest.TextTestRunner(stream=stream, verbosity=2)
    result = runner.run(suite)
    
    # Print results
    output = stream.getvalue()
    print(output)
    
    return result


def run_specific_test(test_module):
    """Run a specific test module."""
    try:
        # Import the test module
        module = __import__(f'test_{test_module}', fromlist=[''])
        
        # Create test suite
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result
    except ImportError as e:
        print(f"Error importing test module 'test_{test_module}': {e}")
        return None


def print_test_summary(result):
    """Print a summary of test results."""
    if result is None:
        return
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Total tests run: {total_tests}")
    print(f"Successful: {total_tests - failures - errors}")
    print(f"Failures: {failures}")
    print(f"Errors: {errors}")
    print(f"Skipped: {skipped}")
    
    if failures > 0:
        print(f"\nFAILURES ({failures}):")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if errors > 0:
        print(f"\nERRORS ({errors}):")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    if skipped > 0:
        print(f"\nSKIPPED ({skipped}):")
        for test, reason in result.skipped:
            print(f"  - {test}: {reason}")
    
    success_rate = ((total_tests - failures - errors) / total_tests * 100) if total_tests > 0 else 0
    print(f"\nSuccess rate: {success_rate:.1f}%")
    
    if failures == 0 and errors == 0:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed. Please review the output above.")
    
    print("="*60)


def main():
    """Main test runner function."""
    print("Code Clinics Test Suite")
    print("="*60)
    
    if len(sys.argv) > 1:
        # Run specific test module
        test_module = sys.argv[1]
        print(f"Running tests for module: {test_module}")
        print("-"*60)
        result = run_specific_test(test_module)
    else:
        # Run all tests
        print("Running all tests...")
        print("-"*60)
        result = run_all_tests()
    
    print_test_summary(result)
    
    # Exit with appropriate code
    if result and (result.failures or result.errors):
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == '__main__':
    main()
