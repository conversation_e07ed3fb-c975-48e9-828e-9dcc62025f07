"""
Unit tests for input validators.
"""

import unittest
import sys
import os

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.validators import InputValidator


class TestInputValidator(unittest.TestCase):
    """Test cases for the InputValidator class."""
    
    def test_validate_email_valid_wethinkcode_domains(self):
        """Test validation of valid WeThinkCode email addresses."""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",  # Admin email
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            with self.subTest(email=email):
                is_valid, message = InputValidator.validate_email(email)
                self.assertTrue(is_valid, f"Email {email} should be valid")
                self.assertEqual(message, "")
    
    def test_validate_email_invalid_domains(self):
        """Test validation of invalid email domains."""
        invalid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",  # Missing wethinkcode.co.za
            "<EMAIL>",  # Wrong TLD
            "<EMAIL>",  # Wrong TLD
            "<EMAIL>"
        ]
        
        expected_message = "Email must be from WeThinkCode domain (@student.wethinkcode.co.za or @wethinkcode.co.za)"
        
        for email in invalid_emails:
            with self.subTest(email=email):
                is_valid, message = InputValidator.validate_email(email)
                self.assertFalse(is_valid, f"Email {email} should be invalid")
                self.assertEqual(message, expected_message)
    
    def test_validate_email_invalid_format(self):
        """Test validation of emails with invalid format."""
        invalid_format_emails = [
            "notanemail",
            "@wethinkcode.co.za",
            "user@",
            "<EMAIL>",
            "user.wethinkcode.co.za",  # Missing @
            "user@@wethinkcode.co.za",  # Double @
            "user@wethinkcode",  # Missing TLD
            ""
        ]
        
        for email in invalid_format_emails:
            with self.subTest(email=email):
                is_valid, message = InputValidator.validate_email(email)
                self.assertFalse(is_valid, f"Email {email} should be invalid")
                if email == "":
                    self.assertEqual(message, "Email cannot be empty")
                else:
                    self.assertEqual(message, "Invalid email format")
    
    def test_validate_email_empty(self):
        """Test validation of empty email."""
        is_valid, message = InputValidator.validate_email("")
        self.assertFalse(is_valid)
        self.assertEqual(message, "Email cannot be empty")
    
    def test_validate_username_valid(self):
        """Test validation of valid usernames."""
        valid_usernames = [
            "user123",
            "test_user",
            "john-doe",
            "student1",
            "volunteer_2024",
            "abc",  # Minimum length
            "a" * 20  # Maximum length
        ]
        
        for username in valid_usernames:
            with self.subTest(username=username):
                is_valid, message = InputValidator.validate_username(username)
                self.assertTrue(is_valid, f"Username {username} should be valid")
                self.assertEqual(message, "")
    
    def test_validate_username_too_short(self):
        """Test validation of usernames that are too short."""
        short_usernames = ["a", "ab", ""]
        
        for username in short_usernames:
            with self.subTest(username=username):
                is_valid, message = InputValidator.validate_username(username)
                self.assertFalse(is_valid, f"Username {username} should be invalid")
                if username == "":
                    self.assertEqual(message, "Username cannot be empty")
                else:
                    self.assertEqual(message, "Username must be at least 3 characters long")
    
    def test_validate_username_too_long(self):
        """Test validation of usernames that are too long."""
        long_username = "a" * 21  # 21 characters
        is_valid, message = InputValidator.validate_username(long_username)
        self.assertFalse(is_valid)
        self.assertEqual(message, "Username must be no more than 20 characters long")
    
    def test_validate_username_invalid_characters(self):
        """Test validation of usernames with invalid characters."""
        invalid_usernames = [
            "user@name",
            "user name",  # Space
            "user.name",  # Dot
            "user#name",  # Hash
            "user$name",  # Dollar sign
            "user%name",  # Percent
            "user&name",  # Ampersand
            "user*name",  # Asterisk
            "user+name",  # Plus
            "user=name",  # Equals
            "user?name",  # Question mark
            "user!name",  # Exclamation
            "user(name)",  # Parentheses
            "user[name]",  # Brackets
            "user{name}",  # Braces
            "user|name",  # Pipe
            "user\\name",  # Backslash
            "user/name",  # Forward slash
            "user:name",  # Colon
            "user;name",  # Semicolon
            "user\"name\"",  # Quotes
            "user'name'",  # Single quotes
            "user<name>",  # Angle brackets
            "user,name",  # Comma
        ]
        
        expected_message = "Username can only contain letters, numbers, underscores, and hyphens"
        
        for username in invalid_usernames:
            with self.subTest(username=username):
                is_valid, message = InputValidator.validate_username(username)
                self.assertFalse(is_valid, f"Username {username} should be invalid")
                self.assertEqual(message, expected_message)
    
    def test_validate_rating_valid(self):
        """Test validation of valid ratings."""
        valid_ratings = ["1", "2", "3", "4", "5"]
        
        for rating in valid_ratings:
            with self.subTest(rating=rating):
                is_valid, message = InputValidator.validate_rating(rating)
                self.assertTrue(is_valid, f"Rating {rating} should be valid")
                self.assertEqual(message, "")
    
    def test_validate_rating_invalid_range(self):
        """Test validation of ratings outside valid range."""
        invalid_ratings = ["0", "6", "10", "-1", "-5"]
        expected_message = "Rating must be between 1 and 5"
        
        for rating in invalid_ratings:
            with self.subTest(rating=rating):
                is_valid, message = InputValidator.validate_rating(rating)
                self.assertFalse(is_valid, f"Rating {rating} should be invalid")
                self.assertEqual(message, expected_message)
    
    def test_validate_rating_non_numeric(self):
        """Test validation of non-numeric ratings."""
        non_numeric_ratings = ["abc", "1.5", "two", "", "1a", "a1", "1 2"]
        expected_message = "Rating must be a number"
        
        for rating in non_numeric_ratings:
            with self.subTest(rating=rating):
                is_valid, message = InputValidator.validate_rating(rating)
                self.assertFalse(is_valid, f"Rating {rating} should be invalid")
                self.assertEqual(message, expected_message)
    
    def test_validate_non_empty_valid(self):
        """Test validation of non-empty text."""
        valid_texts = [
            "Hello world",
            "Test",
            "123",
            "Special characters: !@#$%^&*()",
            "   Text with spaces   "  # Should be valid after strip
        ]
        
        for text in valid_texts:
            with self.subTest(text=text):
                is_valid, message = InputValidator.validate_non_empty(text, "Test Field")
                self.assertTrue(is_valid, f"Text '{text}' should be valid")
                self.assertEqual(message, "")
    
    def test_validate_non_empty_invalid(self):
        """Test validation of empty or whitespace-only text."""
        invalid_texts = ["", "   ", "\t", "\n", "\r\n", "  \t  \n  "]
        
        for text in invalid_texts:
            with self.subTest(text=repr(text)):
                is_valid, message = InputValidator.validate_non_empty(text, "Test Field")
                self.assertFalse(is_valid, f"Text '{repr(text)}' should be invalid")
                self.assertEqual(message, "Test Field cannot be empty")
    
    def test_validate_non_empty_custom_field_name(self):
        """Test validation with custom field name."""
        is_valid, message = InputValidator.validate_non_empty("", "Username")
        self.assertFalse(is_valid)
        self.assertEqual(message, "Username cannot be empty")
        
        is_valid, message = InputValidator.validate_non_empty("", "Email Address")
        self.assertFalse(is_valid)
        self.assertEqual(message, "Email Address cannot be empty")
    
    def test_validate_choice_valid(self):
        """Test validation of valid menu choices."""
        test_cases = [
            ("1", 1, 5),
            ("3", 1, 5),
            ("5", 1, 5),
            ("1", 1, 1),  # Single option
            ("10", 1, 10),
            ("0", 0, 5),  # Zero as valid choice
        ]
        
        for choice, min_val, max_val in test_cases:
            with self.subTest(choice=choice, min_val=min_val, max_val=max_val):
                is_valid, message = InputValidator.validate_choice(choice, min_val, max_val)
                self.assertTrue(is_valid, f"Choice {choice} should be valid for range {min_val}-{max_val}")
                self.assertEqual(message, "")
    
    def test_validate_choice_invalid_range(self):
        """Test validation of choices outside valid range."""
        test_cases = [
            ("0", 1, 5, "Choice must be between 1 and 5"),
            ("6", 1, 5, "Choice must be between 1 and 5"),
            ("-1", 1, 5, "Choice must be between 1 and 5"),
            ("2", 3, 5, "Choice must be between 3 and 5"),
            ("11", 1, 10, "Choice must be between 1 and 10"),
        ]
        
        for choice, min_val, max_val, expected_message in test_cases:
            with self.subTest(choice=choice, min_val=min_val, max_val=max_val):
                is_valid, message = InputValidator.validate_choice(choice, min_val, max_val)
                self.assertFalse(is_valid, f"Choice {choice} should be invalid for range {min_val}-{max_val}")
                self.assertEqual(message, expected_message)
    
    def test_validate_choice_non_numeric(self):
        """Test validation of non-numeric choices."""
        non_numeric_choices = ["abc", "1.5", "two", "", "1a", "a1", "1 2"]
        expected_message = "Please enter a valid number"
        
        for choice in non_numeric_choices:
            with self.subTest(choice=choice):
                is_valid, message = InputValidator.validate_choice(choice, 1, 5)
                self.assertFalse(is_valid, f"Choice {choice} should be invalid")
                self.assertEqual(message, expected_message)
    
    def test_edge_cases(self):
        """Test edge cases and boundary conditions."""
        # Test email with exactly the admin email
        is_valid, message = InputValidator.validate_email("<EMAIL>")
        self.assertTrue(is_valid)
        self.assertEqual(message, "")
        
        # Test username with exactly 3 characters
        is_valid, message = InputValidator.validate_username("abc")
        self.assertTrue(is_valid)
        self.assertEqual(message, "")
        
        # Test username with exactly 20 characters
        is_valid, message = InputValidator.validate_username("a" * 20)
        self.assertTrue(is_valid)
        self.assertEqual(message, "")
        
        # Test rating boundaries
        is_valid, message = InputValidator.validate_rating("1")
        self.assertTrue(is_valid)
        
        is_valid, message = InputValidator.validate_rating("5")
        self.assertTrue(is_valid)
        
        # Test choice boundaries
        is_valid, message = InputValidator.validate_choice("1", 1, 1)
        self.assertTrue(is_valid)


if __name__ == '__main__':
    unittest.main()
