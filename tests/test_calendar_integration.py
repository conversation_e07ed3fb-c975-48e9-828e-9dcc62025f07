"""
Unit tests for Google Calendar integration.
"""

import unittest
import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from utils.calendar_integration import CalendarManager
    CALENDAR_AVAILABLE = True
except ImportError:
    CALENDAR_AVAILABLE = False


@unittest.skipUnless(CALENDAR_AVAILABLE, "Google Calendar API not available")
class TestCalendarManager(unittest.TestCase):
    """Test cases for the CalendarManager class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Note: These tests don't require actual Google API authentication
        # They test the logic and structure of the CalendarManager class
        self.calendar_manager = CalendarManager()
    
    def test_calendar_manager_creation(self):
        """Test calendar manager object creation."""
        self.assertIsNotNone(self.calendar_manager)
        self.assertEqual(self.calendar_manager.credentials_file, 'credentials.json')
        self.assertEqual(self.calendar_manager.token_file, 'token.pickle')
        self.assertIsNone(self.calendar_manager.service)
        self.assertIsNone(self.calendar_manager.calendar_id)
        self.assertIsNone(self.calendar_manager.authenticated_user)
    
    def test_calendar_manager_custom_files(self):
        """Test calendar manager with custom credential files."""
        custom_manager = CalendarManager(
            credentials_file='custom_creds.json',
            token_file='custom_token.pickle'
        )
        
        self.assertEqual(custom_manager.credentials_file, 'custom_creds.json')
        self.assertEqual(custom_manager.token_file, 'custom_token.pickle')
    
    def test_is_authorized_user_admin(self):
        """Test authorization check for admin user."""
        admin_email = "<EMAIL>"
        result = self.calendar_manager._is_authorized_user(admin_email)
        self.assertTrue(result)
    
    def test_is_authorized_user_student_domain(self):
        """Test authorization check for student domain users."""
        student_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in student_emails:
            with self.subTest(email=email):
                result = self.calendar_manager._is_authorized_user(email)
                self.assertTrue(result, f"Email {email} should be authorized")
    
    def test_is_authorized_user_staff_domain(self):
        """Test authorization check for staff domain users."""
        staff_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in staff_emails:
            with self.subTest(email=email):
                result = self.calendar_manager._is_authorized_user(email)
                self.assertTrue(result, f"Email {email} should be authorized")
    
    def test_is_authorized_user_unauthorized_domains(self):
        """Test authorization check for unauthorized domain users."""
        unauthorized_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",  # Missing wethinkcode.co.za
            "<EMAIL>",  # Wrong TLD
            "<EMAIL>",
            ""
        ]
        
        for email in unauthorized_emails:
            with self.subTest(email=email):
                result = self.calendar_manager._is_authorized_user(email)
                self.assertFalse(result, f"Email {email} should not be authorized")
    
    def test_create_event_description(self):
        """Test creation of calendar event descriptions."""
        session_data = {
            'session_id': 'test_session_123',
            'topic': 'Python OOP',
            'description': 'Help with classes and inheritance',
            'student_name': 'John Doe',
            'student_email': '<EMAIL>',
            'volunteer_name': 'Jane Smith',
            'volunteer_email': '<EMAIL>'
        }
        
        description = self.calendar_manager._create_event_description(session_data)
        
        # Check that all important information is included
        self.assertIn('Python OOP', description)
        self.assertIn('Help with classes and inheritance', description)
        self.assertIn('John Doe', description)
        self.assertIn('<EMAIL>', description)
        self.assertIn('Jane Smith', description)
        self.assertIn('<EMAIL>', description)
        self.assertIn('test_session_123', description)
        self.assertIn('Code Clinics Session', description)
        self.assertIn('<EMAIL>', description)  # Admin contact
    
    def test_constants_and_configuration(self):
        """Test that constants are properly configured."""
        # Test scopes
        expected_scopes = ['https://www.googleapis.com/auth/calendar']
        self.assertEqual(self.calendar_manager.SCOPES, expected_scopes)
        
        # Test allowed domains
        expected_domains = ['student.wethinkcode.co.za', 'wethinkcode.co.za']
        self.assertEqual(self.calendar_manager.ALLOWED_DOMAINS, expected_domains)
        
        # Test admin email
        expected_admin = '<EMAIL>'
        self.assertEqual(self.calendar_manager.ADMIN_EMAIL, expected_admin)


class TestCalendarIntegrationWithoutAPI(unittest.TestCase):
    """Test calendar integration functionality without requiring Google API."""
    
    def test_calendar_availability_check(self):
        """Test checking if calendar integration is available."""
        # This test will pass regardless of whether the API is installed
        # It tests the import mechanism
        try:
            from utils.calendar_integration import CalendarManager
            available = True
        except ImportError:
            available = False
        
        # The test should reflect the actual availability
        self.assertEqual(available, CALENDAR_AVAILABLE)
    
    def test_session_data_structure(self):
        """Test the structure of session data for calendar events."""
        # Test the expected structure of session data
        session_data = {
            'session_id': 'session_123',
            'topic': 'Test Topic',
            'description': 'Test Description',
            'student_name': 'Test Student',
            'student_email': '<EMAIL>',
            'volunteer_name': 'Test Volunteer',
            'volunteer_email': '<EMAIL>',
            'scheduled_time': datetime.now(),
            'duration_minutes': 60
        }
        
        # Verify all required fields are present
        required_fields = [
            'session_id', 'topic', 'description', 'student_name',
            'student_email', 'volunteer_name', 'volunteer_email',
            'scheduled_time', 'duration_minutes'
        ]
        
        for field in required_fields:
            self.assertIn(field, session_data, f"Required field {field} missing")
        
        # Verify data types
        self.assertIsInstance(session_data['session_id'], str)
        self.assertIsInstance(session_data['topic'], str)
        self.assertIsInstance(session_data['description'], str)
        self.assertIsInstance(session_data['student_name'], str)
        self.assertIsInstance(session_data['student_email'], str)
        self.assertIsInstance(session_data['volunteer_name'], str)
        self.assertIsInstance(session_data['volunteer_email'], str)
        self.assertIsInstance(session_data['scheduled_time'], datetime)
        self.assertIsInstance(session_data['duration_minutes'], int)
    
    def test_calendar_event_structure(self):
        """Test the expected structure of calendar events."""
        # Test the structure that would be sent to Google Calendar API
        start_time = datetime.now()
        end_time = start_time + timedelta(minutes=60)
        
        expected_event_structure = {
            'summary': 'Code Clinic: Test Topic',
            'description': 'Test description',
            'start': {
                'dateTime': start_time.isoformat(),
                'timeZone': 'Africa/Johannesburg',
            },
            'end': {
                'dateTime': end_time.isoformat(),
                'timeZone': 'Africa/Johannesburg',
            },
            'attendees': [
                {'email': '<EMAIL>'},
                {'email': '<EMAIL>'},
            ],
            'reminders': {
                'useDefault': False,
                'overrides': [
                    {'method': 'email', 'minutes': 24 * 60},  # 1 day before
                    {'method': 'popup', 'minutes': 30},       # 30 minutes before
                ],
            },
            'extendedProperties': {
                'private': {
                    'session_id': 'test_session_123',
                    'app': 'code_clinics'
                }
            }
        }
        
        # Verify structure
        self.assertIn('summary', expected_event_structure)
        self.assertIn('description', expected_event_structure)
        self.assertIn('start', expected_event_structure)
        self.assertIn('end', expected_event_structure)
        self.assertIn('attendees', expected_event_structure)
        self.assertIn('reminders', expected_event_structure)
        self.assertIn('extendedProperties', expected_event_structure)
        
        # Verify start/end time structure
        self.assertIn('dateTime', expected_event_structure['start'])
        self.assertIn('timeZone', expected_event_structure['start'])
        self.assertEqual(expected_event_structure['start']['timeZone'], 'Africa/Johannesburg')
        
        # Verify attendees structure
        attendees = expected_event_structure['attendees']
        self.assertEqual(len(attendees), 2)
        self.assertTrue(all('email' in attendee for attendee in attendees))
        
        # Verify reminders structure
        reminders = expected_event_structure['reminders']
        self.assertFalse(reminders['useDefault'])
        self.assertIn('overrides', reminders)
        self.assertEqual(len(reminders['overrides']), 2)
        
        # Verify extended properties
        ext_props = expected_event_structure['extendedProperties']['private']
        self.assertIn('session_id', ext_props)
        self.assertIn('app', ext_props)
        self.assertEqual(ext_props['app'], 'code_clinics')
    
    def test_time_calculations(self):
        """Test time calculations for calendar events."""
        start_time = datetime(2024, 1, 15, 14, 30, 0)  # 2:30 PM
        duration_minutes = 90
        
        expected_end_time = start_time + timedelta(minutes=duration_minutes)
        
        self.assertEqual(expected_end_time, datetime(2024, 1, 15, 16, 0, 0))  # 4:00 PM
        
        # Test different durations
        test_cases = [
            (30, datetime(2024, 1, 15, 15, 0, 0)),   # 30 minutes
            (60, datetime(2024, 1, 15, 15, 30, 0)),  # 1 hour
            (120, datetime(2024, 1, 15, 16, 30, 0)), # 2 hours
        ]
        
        for duration, expected_end in test_cases:
            with self.subTest(duration=duration):
                calculated_end = start_time + timedelta(minutes=duration)
                self.assertEqual(calculated_end, expected_end)
    
    def test_timezone_handling(self):
        """Test timezone handling for South African timezone."""
        expected_timezone = 'Africa/Johannesburg'
        
        # This should be the timezone used for all calendar events
        if CALENDAR_AVAILABLE:
            calendar_manager = CalendarManager()
            # The timezone is used in the calendar event creation
            # We can't test the actual API call, but we can verify the constant
            self.assertTrue(hasattr(calendar_manager, '_create_event_description'))
    
    def test_email_validation_integration(self):
        """Test integration with email validation for calendar users."""
        # Test that calendar manager uses the same domain restrictions
        # as the main application
        
        if CALENDAR_AVAILABLE:
            calendar_manager = CalendarManager()
            
            # Test admin email
            self.assertTrue(calendar_manager._is_authorized_user("<EMAIL>"))
            
            # Test student domain
            self.assertTrue(calendar_manager._is_authorized_user("<EMAIL>"))
            
            # Test staff domain
            self.assertTrue(calendar_manager._is_authorized_user("<EMAIL>"))
            
            # Test invalid domain
            self.assertFalse(calendar_manager._is_authorized_user("<EMAIL>"))


class TestCalendarManagerMockBehavior(unittest.TestCase):
    """Test calendar manager behavior without actual API calls."""
    
    @unittest.skipUnless(CALENDAR_AVAILABLE, "Google Calendar API not available")
    def test_authentication_without_service(self):
        """Test authentication behavior when service is not initialized."""
        calendar_manager = CalendarManager()
        
        # Service should be None initially
        self.assertIsNone(calendar_manager.service)
        self.assertIsNone(calendar_manager.authenticated_user)
        
        # Calendar ID should be None initially
        self.assertIsNone(calendar_manager.calendar_id)
    
    @unittest.skipUnless(CALENDAR_AVAILABLE, "Google Calendar API not available")
    def test_error_handling_structure(self):
        """Test that error handling methods exist and have correct signatures."""
        calendar_manager = CalendarManager()
        
        # Test that methods exist and can be called (even if they fail due to no auth)
        self.assertTrue(hasattr(calendar_manager, 'authenticate'))
        self.assertTrue(hasattr(calendar_manager, 'create_code_clinics_calendar'))
        self.assertTrue(hasattr(calendar_manager, 'find_code_clinics_calendar'))
        self.assertTrue(hasattr(calendar_manager, 'get_or_create_calendar'))
        self.assertTrue(hasattr(calendar_manager, 'create_session_event'))
        self.assertTrue(hasattr(calendar_manager, 'update_session_event'))
        self.assertTrue(hasattr(calendar_manager, 'delete_session_event'))
        self.assertTrue(hasattr(calendar_manager, 'get_upcoming_sessions'))
        
        # Test method signatures (they should be callable)
        self.assertTrue(callable(calendar_manager.authenticate))
        self.assertTrue(callable(calendar_manager._is_authorized_user))
        self.assertTrue(callable(calendar_manager._create_event_description))


if __name__ == '__main__':
    # Print availability status
    if CALENDAR_AVAILABLE:
        print("Google Calendar API is available - running full test suite")
    else:
        print("Google Calendar API not available - running limited tests")
        print("Install dependencies with: pip install -r requirements.txt")
    
    unittest.main()
