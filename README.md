# Code Clinics CLI Application

A command-line interface application that connects coding volunteers with students seeking help, built using Object-Oriented Programming principles in Python.

## Features

### For Students
- **Request Help**: Create help requests with topic, description, and urgency level
- **View Sessions**: Track scheduled, in-progress, and completed sessions
- **Rate Volunteers**: Provide feedback and ratings after sessions
- **Learning Goals**: Manage personal learning objectives
- **Browse Volunteers**: View available volunteers and their expertise

### For Volunteers
- **Accept Requests**: Browse and accept student help requests
- **Conduct Sessions**: Start and end tutoring sessions
- **Manage Availability**: Set availability status
- **Update Expertise**: Manage areas of expertise and programming languages
- **Session Management**: Track conducted sessions and ratings

### General Features
- **User Registration**: Separate registration for students and volunteers (WeThinkCode domain restricted)
- **Session Tracking**: Complete session lifecycle management
- **Rating System**: Bidirectional rating system for quality assurance
- **Data Persistence**: JSON-based data storage
- **Role-based Access**: Different capabilities based on user role
- **Google Calendar Integration**: Schedule sessions with automatic calendar events and email invitations
- **Domain Security**: Restricted to WeThinkCode email domains for security

## Project Structure

```
Code_Clinics/
├── main.py                 # Application entry point
├── models/                 # Core data models
│   ├── __init__.py
│   ├── user.py            # Abstract base User class
│   ├── student.py         # Student class
│   ├── volunteer.py       # Volunteer class
│   ├── session.py         # Session class
│   └── clinic_manager.py  # Main application logic
├── cli/                   # Command-line interface
│   ├── __init__.py
│   ├── interface.py       # Main CLI interface
│   └── menus.py          # Menu systems
├── utils/                 # Utility functions
│   ├── __init__.py
│   ├── validators.py      # Input validation (with domain restrictions)
│   ├── helpers.py        # Helper functions
│   └── calendar_integration.py # Google Calendar API integration
├── data/                  # Data storage (created at runtime)
│   ├── users.json        # User data
│   ├── sessions.json     # Session data
│   └── help_requests.json # Help request data
├── test_app.py           # Automated test script
├── test_calendar_integration.py # Calendar integration tests
├── credentials.json      # Google Calendar API credentials
├── requirements.txt      # Dependencies (Google Calendar API)
└── README.md            # This file
```

## Object-Oriented Design

### Class Hierarchy

```
User (Abstract Base Class)
├── Student
└── Volunteer

Session (Independent Class)
ClinicManager (Main Controller)
CLIInterface (User Interface)
MenuSystem (Menu Navigation)
```

### Key OOP Principles Applied

1. **Inheritance**: Student and Volunteer inherit from abstract User base class
2. **Encapsulation**: Data and methods are properly encapsulated within classes
3. **Abstraction**: Abstract User class defines common interface
4. **Polymorphism**: Different user types implement role-specific behaviors
5. **Composition**: ClinicManager composes User and Session objects

## Installation and Setup

1. **Clone or download the project**:
   ```bash
   git clone <repository-url>
   cd Code_Clinics
   ```

2. **Ensure Python 3.7+ is installed**:
   ```bash
   python --version
   ```

3. **Install dependencies for Google Calendar integration**:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Running the Application

```bash
python main.py
```

### First Time Setup

**Important**: Only WeThinkCode email addresses are allowed:
- Students: `@student.wethinkcode.co.za`
- Staff/Volunteers: `@wethinkcode.co.za`
- Admin: `<EMAIL>`

1. **Register as a Student**:
   - Choose option 2 from main menu
   - Provide username, WeThinkCode email, full name
   - Select skill level (beginner/intermediate/advanced)
   - Add learning goals

2. **Register as a Volunteer**:
   - Choose option 3 from main menu
   - Provide username, WeThinkCode email, full name
   - Select experience level (intermediate/advanced/expert)
   - Add expertise areas

3. **Setup Google Calendar Integration** (Optional):
   - Login to your account
   - Go to Calendar Integration menu
   - Authenticate with your WeThinkCode Google account
   - Grant calendar permissions

### Student Workflow

1. **Login** with your username
2. **Request Help**:
   - Specify topic and description
   - Set urgency level
   - Wait for volunteer to accept
3. **Participate in Session**:
   - Volunteer will start the session
   - Collaborate on the problem
   - Session ends when complete
4. **Rate the Session**:
   - Provide rating (1-5 stars)
   - Leave feedback for volunteer

### Volunteer Workflow

1. **Login** with your username
2. **View Help Requests**:
   - Browse open student requests
   - See topic, description, urgency
3. **Accept Request**:
   - Choose request to accept
   - Session is automatically created
4. **Conduct Session**:
   - Start session when ready
   - Help student with their problem
   - End session with notes
5. **Rate the Session**:
   - Provide rating for student
   - Leave feedback

## Data Storage

The application uses JSON files for data persistence:

- **users.json**: Stores all user accounts (students and volunteers)
- **sessions.json**: Stores all session records
- **help_requests.json**: Stores help requests and their status

Data is automatically saved after each operation and loaded on application startup.

## Google Calendar Integration

The Code Clinics application includes comprehensive Google Calendar integration for scheduling and managing sessions.

### Features
- **Automatic Calendar Events**: Sessions are automatically added to Google Calendar
- **Email Invitations**: Participants receive calendar invitations via email
- **Domain Security**: Only WeThinkCode email addresses can authenticate
- **Session Reminders**: Automatic email and popup reminders
- **Real-time Sync**: Calendar events update when sessions are modified

### Setup Process

1. **Prerequisites**:
   - WeThinkCode Google account (`@student.wethinkcode.co.za` or `@wethinkcode.co.za`)
   - Google Calendar API credentials (already configured)
   - Internet connection for authentication

2. **Authentication Steps**:
   ```bash
   # Run the application
   python main.py

   # Login with your WeThinkCode account
   # Navigate to Calendar Integration menu
   # Choose "Authenticate with Google Calendar"
   # Follow browser authentication flow
   ```

3. **First-time Authentication**:
   - Browser window will open automatically
   - Login with your WeThinkCode Google account
   - Grant calendar permissions to the application
   - Authentication token is saved for future use

### Calendar Workflow

1. **Session Creation**:
   - Volunteer accepts student help request
   - Session is created in local database
   - Optional: Schedule with Google Calendar

2. **Calendar Scheduling**:
   - Select session from Calendar Integration menu
   - Choose date/time for the session
   - Set session duration (default: 60 minutes)
   - Calendar event is created automatically

3. **Automatic Features**:
   - Email invitations sent to both participants
   - Calendar reminders set (1 day and 30 minutes before)
   - Session details included in event description
   - Unique session ID for tracking

4. **Session Updates**:
   - Calendar events update when sessions are completed/cancelled
   - Session notes are added to calendar event description
   - Event status reflects session status

### Security and Permissions

- **Domain Restriction**: Only `@student.wethinkcode.co.za` and `@wethinkcode.co.za` emails allowed
- **Admin Access**: `<EMAIL>` has full administrative access
- **Calendar Permissions**: Application requests minimal required permissions
- **Data Privacy**: Session data is only shared with participants

### Troubleshooting

**Authentication Issues**:
- Ensure you're using a WeThinkCode email address
- Check internet connection
- Clear browser cache if authentication fails
- Re-run authentication process if token expires

**Calendar Not Syncing**:
- Verify Google Calendar permissions
- Check if calendar service is accessible
- Re-authenticate if necessary

**Email Domain Errors**:
- Only WeThinkCode domains are allowed
- Contact admin if you need domain access
- Verify email format is correct

## Class Documentation

### User (Abstract Base Class)
- **Purpose**: Defines common interface for all users
- **Key Methods**: `login()`, `get_role()`, `get_capabilities()`
- **Attributes**: `user_id`, `username`, `email`, `full_name`, `created_at`

### Student Class
- **Inherits**: User
- **Purpose**: Represents students seeking help
- **Key Methods**: `request_help()`, `add_learning_goal()`
- **Attributes**: `skill_level`, `learning_goals`, `completed_sessions`

### Volunteer Class
- **Inherits**: User
- **Purpose**: Represents volunteers offering help
- **Key Methods**: `can_help_with()`, `add_rating()`, `set_availability()`
- **Attributes**: `expertise_areas`, `programming_languages`, `rating`

### Session Class
- **Purpose**: Manages tutoring sessions
- **Key Methods**: `start_session()`, `end_session()`, `add_rating()`
- **Attributes**: `topic`, `status`, `student_rating`, `volunteer_rating`

### ClinicManager Class
- **Purpose**: Main application controller
- **Key Methods**: `register_user()`, `create_help_request()`, `accept_help_request()`
- **Responsibilities**: User management, session coordination, data persistence

## Features in Detail

### Rating System
- Students rate volunteers (1-5 stars)
- Volunteers rate students (1-5 stars)
- Volunteer ratings are aggregated for reputation
- Feedback text is stored with ratings

### Session Lifecycle
1. **Scheduled**: Session created when volunteer accepts request
2. **In Progress**: Session started by volunteer
3. **Completed**: Session ended with notes
4. **Cancelled**: Session cancelled by either party

### User Capabilities
- **Students**: Request help, join sessions, rate volunteers, manage learning goals
- **Volunteers**: Accept requests, conduct sessions, set availability, update expertise

## Error Handling

- Input validation for all user inputs
- Graceful handling of file I/O errors
- User-friendly error messages
- Keyboard interrupt handling (Ctrl+C)

## Future Enhancements

- **Scheduling**: Allow scheduling sessions for specific times
- **Messaging**: In-app messaging between users
- **Search**: Advanced search for volunteers by expertise
- **Statistics**: Usage statistics and analytics
- **Notifications**: Email or push notifications
- **Web Interface**: Web-based interface in addition to CLI

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following OOP principles
4. Add appropriate documentation
5. Test your changes
6. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For questions or issues, please create an issue in the repository or contact the development team.
