#!/usr/bin/env python3
"""
Code Clinics CLI Application
A platform connecting coding volunteers with students seeking help.
"""

from cli.interface import CLIInterface
from models.clinic_manager import ClinicManager


def main():
    """Main entry point for the Code Clinics application."""
    print("=" * 50)
    print("    Welcome to Code Clinics!")
    print("    Connecting Students with Volunteers")
    print("=" * 50)
    
    # Initialize the clinic manager and CLI interface
    clinic_manager = ClinicManager()
    cli_interface = CLIInterface(clinic_manager)
    
    # Start the application
    cli_interface.run()


if __name__ == "__main__":
    main()
