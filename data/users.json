[{"user_id": "65ae108e-9e3a-4238-bbb5-34c5b41d0b48", "username": "john_student", "email": "<EMAIL>", "full_name": "<PERSON>", "created_at": "2025-06-02T21:03:11.237995", "last_login": "2025-06-02T21:05:34.417397", "is_active": true, "role": "Student", "skill_level": "beginner", "learning_goals": ["Learn Python basics", "Understand OOP concepts"], "help_requests": ["e4bd2ea6-3f5f-4924-bc9c-21460daf01ea"], "completed_sessions": [], "preferred_languages": []}, {"user_id": "9c94a2d9-d649-48cd-a51e-0d191785b774", "username": "alice_mentor", "email": "<EMAIL>", "full_name": "<PERSON>", "created_at": "2025-06-02T21:05:11.764811", "last_login": "2025-06-02T21:06:46.838864", "is_active": true, "role": "Volunteer", "expertise_areas": ["Python Programming", "Object-Oriented Programming", "Web Development"], "experience_level": "advanced", "programming_languages": [], "availability_hours": [], "sessions_conducted": [], "rating": 0.0, "total_ratings": 0, "is_available": true}, {"user_id": "16e762f5-18bc-4c84-84bc-8fc259bf0c3b", "username": "test_student", "email": "<EMAIL>", "full_name": "Test Student", "created_at": "2025-06-02T21:09:50.708776", "last_login": "2025-06-02T21:09:50.710560", "is_active": true, "role": "Student", "skill_level": "beginner", "learning_goals": ["Learn Python", "Understand OOP"], "help_requests": ["529ed746-8912-4134-8913-ae6a2575695f"], "completed_sessions": [], "preferred_languages": []}, {"user_id": "1cdee24f-6d87-41bd-b36c-0ce673fc51fe", "username": "test_volunteer", "email": "<EMAIL>", "full_name": "Test Volunteer", "created_at": "2025-06-02T21:09:50.709687", "last_login": "2025-06-02T21:09:50.711856", "is_active": true, "role": "Volunteer", "expertise_areas": ["Python", "OOP", "Web Development"], "experience_level": "advanced", "programming_languages": [], "availability_hours": [], "sessions_conducted": [], "rating": 0.0, "total_ratings": 0, "is_available": true}, {"user_id": "41ce9276-927e-45fc-ad43-8de03795169a", "username": "admin_user", "email": "<EMAIL>", "full_name": "Admin User", "created_at": "2025-06-02T21:18:25.167459", "last_login": null, "is_active": true, "role": "Volunteer", "expertise_areas": ["System Administration", "Python", "Project Management"], "experience_level": "expert", "programming_languages": [], "availability_hours": [], "sessions_conducted": [], "rating": 0.0, "total_ratings": 0, "is_available": true}, {"user_id": "2efa50f1-4bf5-4414-a428-8e29c126e006", "username": "test_student_wtc", "email": "<EMAIL>", "full_name": "Test Student WTC", "created_at": "2025-06-02T21:18:25.168820", "last_login": "2025-06-02T21:18:25.173829", "is_active": true, "role": "Student", "skill_level": "beginner", "learning_goals": ["Learn Python", "Understand OOP", "Calendar Integration"], "help_requests": ["4f143e83-ed8b-4ec8-81e6-61078d43d617"], "completed_sessions": [], "preferred_languages": []}, {"user_id": "66da658e-90a1-40bd-98d0-8a08a78959b6", "username": "test_volunteer_wtc", "email": "<EMAIL>", "full_name": "Test Volunteer WTC", "created_at": "2025-06-02T21:18:25.169532", "last_login": "2025-06-02T21:18:25.177853", "is_active": true, "role": "Volunteer", "expertise_areas": ["Python", "Calendar APIs", "Web Development"], "experience_level": "advanced", "programming_languages": [], "availability_hours": [], "sessions_conducted": [], "rating": 0.0, "total_ratings": 0, "is_available": true}, {"user_id": "917cba9b-90a2-4e3d-a2c7-a67354ecc1e6", "username": "invalid_user", "email": "<EMAIL>", "full_name": "Invalid User", "created_at": "2025-06-02T21:18:25.171254", "last_login": null, "is_active": true, "role": "Student", "skill_level": "beginner", "learning_goals": [], "help_requests": [], "completed_sessions": [], "preferred_languages": []}]