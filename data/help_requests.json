{"e4bd2ea6-3f5f-4924-bc9c-21460daf01ea": {"request_id": "e4bd2ea6-3f5f-4924-bc9c-21460daf01ea", "student_id": "65ae108e-9e3a-4238-bbb5-34c5b41d0b48", "student_name": "<PERSON>", "topic": "Python Classes and Objects", "description": "I'm struggling to understand how to create classes and use inheritance in Python. Need help with basic OOP concepts.", "urgency": "normal", "created_at": "2025-06-02T21:06:21.634823", "status": "accepted", "volunteer_id": "9c94a2d9-d649-48cd-a51e-0d191785b774", "session_id": "124a9c03-d817-4303-bc85-dc6446cf3f75"}, "529ed746-8912-4134-8913-ae6a2575695f": {"request_id": "529ed746-8912-4134-8913-ae6a2575695f", "student_id": "16e762f5-18bc-4c84-84bc-8fc259bf0c3b", "student_name": "Test Student", "topic": "Python Classes", "description": "Need help with class inheritance", "urgency": "normal", "created_at": "2025-06-02T21:09:50.711459", "status": "accepted", "volunteer_id": "1cdee24f-6d87-41bd-b36c-0ce673fc51fe", "session_id": "89379477-dbbc-4b01-8602-731b32296785"}, "4f143e83-ed8b-4ec8-81e6-61078d43d617": {"request_id": "4f143e83-ed8b-4ec8-81e6-61078d43d617", "student_id": "2efa50f1-4bf5-4414-a428-8e29c126e006", "student_name": "Test Student WTC", "topic": "Google Calendar Integration", "description": "Need help understanding how to integrate Google Calendar API with Python applications", "urgency": "normal", "created_at": "2025-06-02T21:18:25.175147", "status": "accepted", "volunteer_id": "66da658e-90a1-40bd-98d0-8a08a78959b6", "session_id": "01206f60-5cf9-49b0-8903-689b7310ec90"}}