#!/usr/bin/env python3
"""
Test script for the new slot-based Code Clinics system.
"""

import sys
import os
import tempfile
import shutil
from datetime import datetime, timedelta

# Add current directory to path
sys.path.insert(0, '.')

def test_slot_system():
    """Test the new slot-based system."""
    print("Testing Slot-Based Code Clinics System")
    print("=" * 50)
    
    try:
        # Import required modules
        from models.clinic_manager import ClinicManager
        from models.time_slot import TimeSlot
        from models.slot_manager import SlotManager
        from utils.slot_display import SlotDisplayManager
        print("✓ All modules imported successfully")
        
        # Create temporary directory for testing
        test_dir = tempfile.mkdtemp()
        print(f"✓ Test directory created: {test_dir}")
        
        try:
            # Initialize clinic manager
            clinic_manager = ClinicManager(data_dir=test_dir)
            print("✓ ClinicManager initialized")
            
            # Test slot manager
            slot_manager = clinic_manager.slot_manager
            print("✓ SlotManager accessible")
            
            # Test working hours calculation
            working_days = slot_manager.get_next_working_days(5)
            print(f"✓ Next 5 working days calculated: {len(working_days)} days")
            
            if working_days:
                sample_day = working_days[0]
                time_slots = slot_manager.get_working_hours_slots(sample_day)
                print(f"✓ Time slots for {sample_day.strftime('%A')}: {len(time_slots)} slots (08:00-15:00)")
                
                # Show first few slots
                print("   Sample slots:")
                for i, slot_time in enumerate(time_slots[:5]):
                    print(f"     {slot_time.strftime('%H:%M')}")
                if len(time_slots) > 5:
                    print(f"     ... and {len(time_slots) - 5} more")
            
            # Register test users
            print("\n--- Testing User Registration ---")
            
            # Register volunteer
            volunteer_success = clinic_manager.register_user(
                username="test_volunteer",
                email="<EMAIL>",
                full_name="Test Volunteer",
                role="volunteer",
                experience_level="advanced",
                expertise_areas=["Python", "Web Development"]
            )
            print(f"✓ Volunteer registration: {volunteer_success}")
            
            # Register student
            student_success = clinic_manager.register_user(
                username="test_student",
                email="<EMAIL>",
                full_name="Test Student",
                role="student",
                skill_level="beginner",
                learning_goals=["Python", "OOP"]
            )
            print(f"✓ Student registration: {student_success}")
            
            # Test slot creation (as volunteer)
            print("\n--- Testing Slot Creation ---")
            clinic_manager.login_user("test_volunteer")
            print("✓ Volunteer logged in")
            
            # Create a slot for tomorrow at 10:00
            tomorrow = datetime.now() + timedelta(days=1)
            # Ensure it's a weekday
            while tomorrow.weekday() >= 5:  # Skip weekends
                tomorrow += timedelta(days=1)
            
            slot_time = tomorrow.replace(hour=10, minute=0, second=0, microsecond=0)
            success, message = clinic_manager.create_time_slot(
                slot_time, 
                "Python Basics", 
                "Help with Python fundamentals"
            )
            print(f"✓ Slot creation: {success} - {message}")
            
            # Test getting available slots
            available_slots = clinic_manager.get_available_slots()
            print(f"✓ Available slots retrieved: {len(available_slots)} slots")
            
            # Test slot booking (as student)
            print("\n--- Testing Slot Booking ---")
            clinic_manager.logout_user()
            clinic_manager.login_user("test_student")
            print("✓ Student logged in")
            
            if available_slots:
                slot_to_book = available_slots[0]
                success, message = clinic_manager.book_time_slot(slot_to_book.slot_id)
                print(f"✓ Slot booking: {success} - {message}")
                
                # Test getting student bookings
                my_bookings = clinic_manager.get_my_slots()
                print(f"✓ Student bookings retrieved: {len(my_bookings)} bookings")
            
            # Test display functionality
            print("\n--- Testing Display System ---")
            try:
                display_manager = SlotDisplayManager(slot_manager)
                print("✓ SlotDisplayManager created")
                
                # Test simple display (fallback when prettytable not available)
                if available_slots:
                    simple_display = display_manager._display_simple_list(available_slots, "Test Slots")
                    print("✓ Simple display format working")
                    print("   Sample output:")
                    lines = simple_display.split('\n')
                    for line in lines[:5]:  # Show first 5 lines
                        print(f"     {line}")
                    if len(lines) > 5:
                        print(f"     ... and {len(lines) - 5} more lines")
                
            except Exception as e:
                print(f"⚠ Display system error (expected if prettytable not installed): {e}")
            
            # Test slot completion and rating
            print("\n--- Testing Session Completion ---")
            clinic_manager.logout_user()
            clinic_manager.login_user("test_volunteer")
            
            if available_slots and len(clinic_manager.get_my_slots()) > 0:
                volunteer_slots = clinic_manager.get_my_slots()
                booked_slots = [s for s in volunteer_slots if s.status == "booked"]
                
                if booked_slots:
                    slot = booked_slots[0]
                    success, message = clinic_manager.complete_slot_session(slot.slot_id, "Session completed successfully")
                    print(f"✓ Session completion: {success} - {message}")
                    
                    # Test rating
                    clinic_manager.logout_user()
                    clinic_manager.login_user("test_student")
                    
                    success, message = clinic_manager.rate_slot_session(slot.slot_id, 5, "Excellent help!")
                    print(f"✓ Student rating: {success} - {message}")
            
            print("\n--- Testing Data Persistence ---")
            # Test saving and loading
            clinic_manager.save_data()
            slot_manager.save_slots()
            print("✓ Data saved successfully")
            
            # Create new managers to test loading
            new_clinic_manager = ClinicManager(data_dir=test_dir)
            print(f"✓ Data loaded: {len(new_clinic_manager.users)} users, {len(new_clinic_manager.slot_manager.slots)} slots")
            
            return True
            
        finally:
            # Clean up
            shutil.rmtree(test_dir)
            print(f"✓ Test directory cleaned up")
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_slot_model():
    """Test the TimeSlot model."""
    print("\n--- Testing TimeSlot Model ---")
    
    try:
        from models.time_slot import TimeSlot
        
        # Create a test slot
        start_time = datetime.now() + timedelta(hours=1)
        slot = TimeSlot(
            volunteer_id="vol_123",
            start_time=start_time,
            topic="Python Help",
            description="Help with Python basics"
        )
        
        print(f"✓ TimeSlot created: {slot.slot_id[:8]}...")
        print(f"✓ Formatted time: {slot.get_formatted_time()}")
        print(f"✓ Formatted date: {slot.get_formatted_date()}")
        print(f"✓ Day name: {slot.get_day_name()}")
        print(f"✓ Is available: {slot.is_available()}")
        print(f"✓ Can be booked: {slot.can_be_booked()}")
        
        # Test booking
        success = slot.book_slot("student_456")
        print(f"✓ Booking successful: {success}")
        print(f"✓ Is booked: {slot.is_booked()}")
        print(f"✓ Student ID: {slot.student_id}")
        
        # Test completion
        success = slot.complete_session("Session completed")
        print(f"✓ Completion successful: {success}")
        print(f"✓ Status: {slot.status}")
        
        # Test ratings
        success = slot.add_student_rating(5, "Great help!")
        print(f"✓ Student rating added: {success}")
        
        success = slot.add_volunteer_rating(4, "Good student")
        print(f"✓ Volunteer rating added: {success}")
        
        # Test serialization
        slot_dict = slot.to_dict()
        restored_slot = TimeSlot.from_dict(slot_dict)
        print(f"✓ Serialization working: {restored_slot.slot_id == slot.slot_id}")
        
        return True
        
    except Exception as e:
        print(f"✗ TimeSlot test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Code Clinics Slot-Based System Test Suite")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # Test TimeSlot model
    if test_time_slot_model():
        tests_passed += 1
        print("✅ TimeSlot model tests PASSED")
    else:
        print("❌ TimeSlot model tests FAILED")
    
    # Test slot system integration
    if test_slot_system():
        tests_passed += 1
        print("✅ Slot system integration tests PASSED")
    else:
        print("❌ Slot system integration tests FAILED")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {tests_passed}/{total_tests} test suites passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! The slot-based system is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please review the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
