"""
CLI Interface for the Code Clinics application.
"""

import sys
from typing import Optional
from models.clinic_manager import ClinicManager
from models.student import Student
from models.volunteer import Volunteer
from utils.validators import InputValidator
from .menus import MenuSystem


class CLIInterface:
    """Main CLI interface for the Code Clinics application."""
    
    def __init__(self, clinic_manager: ClinicManager):
        """
        Initialize the CLI interface.
        
        Args:
            clinic_manager (ClinicManager): The clinic manager instance
        """
        self.clinic_manager = clinic_manager
        self.validator = InputValidator()
        self.menu_system = MenuSystem(clinic_manager)
        self.running = True
    
    def run(self):
        """Main application loop."""
        while self.running:
            try:
                if self.clinic_manager.current_user is None:
                    self.show_main_menu()
                else:
                    self.show_user_dashboard()
            except KeyboardInterrupt:
                print("\n\nGoodbye! Thanks for using Code Clinics!")
                self.running = False
            except Exception as e:
                print(f"\nAn error occurred: {e}")
                print("Please try again.")
    
    def show_main_menu(self):
        """Show the main menu for non-authenticated users."""
        print("\n" + "="*50)
        print("           CODE CLINICS - MAIN MENU")
        print("="*50)
        print("1. Login")
        print("2. Register as Student")
        print("3. Register as Volunteer")
        print("4. View Available Volunteers")
        print("5. Exit")
        print("-"*50)
        
        choice = input("Please select an option (1-5): ").strip()
        
        if choice == "1":
            self.handle_login()
        elif choice == "2":
            self.handle_student_registration()
        elif choice == "3":
            self.handle_volunteer_registration()
        elif choice == "4":
            self.show_available_volunteers()
        elif choice == "5":
            self.running = False
            print("Goodbye! Thanks for using Code Clinics!")
        else:
            print("Invalid option. Please try again.")
    
    def handle_login(self):
        """Handle user login."""
        print("\n" + "-"*30)
        print("           LOGIN")
        print("-"*30)
        
        username = input("Username: ").strip()
        if not username:
            print("Username cannot be empty.")
            return
        
        if self.clinic_manager.login_user(username):
            user = self.clinic_manager.current_user
            print(f"\nWelcome back, {user.full_name}!")
            print(f"Logged in as: {user.get_role()}")
        else:
            print("Login failed. Username not found or account inactive.")
    
    def handle_student_registration(self):
        """Handle student registration."""
        print("\n" + "-"*40)
        print("        STUDENT REGISTRATION")
        print("-"*40)
        
        # Get basic info
        username = self.get_valid_input("Username: ", self.validator.validate_username)
        if not username:
            return
        
        email = self.get_valid_input("Email: ", self.validator.validate_email)
        if not email:
            return
        
        full_name = input("Full Name: ").strip()
        if not full_name:
            print("Full name cannot be empty.")
            return
        
        # Get student-specific info
        print("\nSkill Level:")
        print("1. Beginner")
        print("2. Intermediate")
        print("3. Advanced")
        skill_choice = input("Select skill level (1-3): ").strip()
        skill_levels = {"1": "beginner", "2": "intermediate", "3": "advanced"}
        skill_level = skill_levels.get(skill_choice, "beginner")
        
        # Get learning goals
        print("\nLearning Goals (press Enter when done):")
        learning_goals = []
        while True:
            goal = input(f"Goal {len(learning_goals) + 1}: ").strip()
            if not goal:
                break
            learning_goals.append(goal)
        
        # Register the student
        success = self.clinic_manager.register_user(
            username=username,
            email=email,
            full_name=full_name,
            role="student",
            skill_level=skill_level,
            learning_goals=learning_goals
        )
        
        if success:
            print(f"\nStudent registration successful! Welcome, {full_name}!")
            print("You can now login with your username.")
        else:
            print("Registration failed. Username might already exist.")
    
    def handle_volunteer_registration(self):
        """Handle volunteer registration."""
        print("\n" + "-"*40)
        print("       VOLUNTEER REGISTRATION")
        print("-"*40)
        
        # Get basic info
        username = self.get_valid_input("Username: ", self.validator.validate_username)
        if not username:
            return
        
        email = self.get_valid_input("Email: ", self.validator.validate_email)
        if not email:
            return
        
        full_name = input("Full Name: ").strip()
        if not full_name:
            print("Full name cannot be empty.")
            return
        
        # Get volunteer-specific info
        print("\nExperience Level:")
        print("1. Intermediate")
        print("2. Advanced")
        print("3. Expert")
        exp_choice = input("Select experience level (1-3): ").strip()
        exp_levels = {"1": "intermediate", "2": "advanced", "3": "expert"}
        experience_level = exp_levels.get(exp_choice, "intermediate")
        
        # Get expertise areas
        print("\nExpertise Areas (press Enter when done):")
        expertise_areas = []
        while True:
            area = input(f"Area {len(expertise_areas) + 1}: ").strip()
            if not area:
                break
            expertise_areas.append(area)
        
        # Register the volunteer
        success = self.clinic_manager.register_user(
            username=username,
            email=email,
            full_name=full_name,
            role="volunteer",
            experience_level=experience_level,
            expertise_areas=expertise_areas
        )
        
        if success:
            print(f"\nVolunteer registration successful! Welcome, {full_name}!")
            print("You can now login with your username.")
        else:
            print("Registration failed. Username might already exist.")
    
    def show_available_volunteers(self):
        """Show list of available volunteers."""
        volunteers = self.clinic_manager.get_available_volunteers()
        
        print("\n" + "-"*50)
        print("           AVAILABLE VOLUNTEERS")
        print("-"*50)
        
        if not volunteers:
            print("No volunteers are currently available.")
            return
        
        for i, volunteer in enumerate(volunteers, 1):
            print(f"\n{i}. {volunteer.full_name}")
            print(f"   Experience: {volunteer.experience_level.title()}")
            print(f"   Rating: {volunteer.rating:.1f}/5.0 ({volunteer.total_ratings} ratings)")
            if volunteer.expertise_areas:
                print(f"   Expertise: {', '.join(volunteer.expertise_areas)}")
            if volunteer.programming_languages:
                print(f"   Languages: {', '.join(volunteer.programming_languages)}")
        
        input("\nPress Enter to continue...")
    
    def show_user_dashboard(self):
        """Show dashboard based on user role."""
        user = self.clinic_manager.current_user
        
        if isinstance(user, Student):
            self.menu_system.show_student_menu()
        elif isinstance(user, Volunteer):
            self.menu_system.show_volunteer_menu()
    
    def get_valid_input(self, prompt: str, validator_func) -> Optional[str]:
        """
        Get valid input using a validator function.
        
        Args:
            prompt (str): Input prompt
            validator_func: Function to validate input
            
        Returns:
            str: Valid input or None if cancelled
        """
        while True:
            value = input(prompt).strip()
            if not value:
                print("Input cannot be empty. Press Ctrl+C to cancel.")
                continue
            
            is_valid, message = validator_func(value)
            if is_valid:
                return value
            else:
                print(f"Invalid input: {message}")
                continue
