"""
Menu system for the Code Clinics CLI application.
"""

from datetime import datetime
from typing import List
from models.clinic_manager import ClinicManager
from models.student import Student
from models.volunteer import Volunteer
from models.session import Session


class MenuSystem:
    """Handles menu display and navigation for different user roles."""
    
    def __init__(self, clinic_manager: ClinicManager):
        """
        Initialize the menu system.
        
        Args:
            clinic_manager (ClinicManager): The clinic manager instance
        """
        self.clinic_manager = clinic_manager
    
    def show_student_menu(self):
        """Show menu options for students."""
        user = self.clinic_manager.current_user
        print(f"\n" + "="*50)
        print(f"    STUDENT DASHBOARD - Welcome {user.full_name}!")
        print("="*50)
        print("1. Request Help")
        print("2. View My Sessions")
        print("3. View Available Volunteers")
        print("4. Rate a Session")
        print("5. Update Learning Goals")
        print("6. View My Profile")
        print("7. Calendar Integration")
        print("8. Logout")
        print("-"*50)
        
        choice = input("Please select an option (1-8): ").strip()
        
        if choice == "1":
            self.handle_help_request()
        elif choice == "2":
            self.show_user_sessions()
        elif choice == "3":
            self.show_available_volunteers_detailed()
        elif choice == "4":
            self.handle_session_rating()
        elif choice == "5":
            self.update_learning_goals()
        elif choice == "6":
            self.show_student_profile()
        elif choice == "7":
            self.handle_calendar_integration()
        elif choice == "8":
            self.clinic_manager.logout_user()
            print("Logged out successfully!")
        else:
            print("Invalid option. Please try again.")
    
    def show_volunteer_menu(self):
        """Show menu options for volunteers."""
        user = self.clinic_manager.current_user
        print(f"\n" + "="*50)
        print(f"   VOLUNTEER DASHBOARD - Welcome {user.full_name}!")
        print("="*50)
        print("1. View Help Requests")
        print("2. Accept Help Request")
        print("3. View My Sessions")
        print("4. Start/End Session")
        print("5. Rate a Session")
        print("6. Update Availability")
        print("7. Update Expertise")
        print("8. View My Profile")
        print("9. Calendar Integration")
        print("10. Logout")
        print("-"*50)
        
        choice = input("Please select an option (1-10): ").strip()
        
        if choice == "1":
            self.show_help_requests()
        elif choice == "2":
            self.handle_accept_request()
        elif choice == "3":
            self.show_user_sessions()
        elif choice == "4":
            self.handle_session_management()
        elif choice == "5":
            self.handle_session_rating()
        elif choice == "6":
            self.update_volunteer_availability()
        elif choice == "7":
            self.update_volunteer_expertise()
        elif choice == "8":
            self.show_volunteer_profile()
        elif choice == "9":
            self.handle_calendar_integration()
        elif choice == "10":
            self.clinic_manager.logout_user()
            print("Logged out successfully!")
        else:
            print("Invalid option. Please try again.")
    
    def handle_help_request(self):
        """Handle student help request creation."""
        print("\n" + "-"*40)
        print("           REQUEST HELP")
        print("-"*40)
        
        topic = input("Topic/Subject: ").strip()
        if not topic:
            print("Topic cannot be empty.")
            return
        
        description = input("Describe your problem: ").strip()
        if not description:
            print("Description cannot be empty.")
            return
        
        print("\nUrgency Level:")
        print("1. Low")
        print("2. Normal")
        print("3. High")
        urgency_choice = input("Select urgency (1-3): ").strip()
        urgency_map = {"1": "low", "2": "normal", "3": "high"}
        urgency = urgency_map.get(urgency_choice, "normal")
        
        request_id = self.clinic_manager.create_help_request(topic, description, urgency)
        
        if request_id:
            print(f"\nHelp request created successfully!")
            print(f"Request ID: {request_id}")
            print("Volunteers will be notified and can accept your request.")
        else:
            print("Failed to create help request.")
    
    def show_help_requests(self):
        """Show open help requests for volunteers."""
        requests = self.clinic_manager.get_open_help_requests()
        
        print("\n" + "-"*50)
        print("           OPEN HELP REQUESTS")
        print("-"*50)
        
        if not requests:
            print("No open help requests at the moment.")
            input("\nPress Enter to continue...")
            return
        
        for i, req in enumerate(requests, 1):
            print(f"\n{i}. Topic: {req['topic']}")
            print(f"   Student: {req['student_name']}")
            print(f"   Description: {req['description']}")
            print(f"   Urgency: {req['urgency'].title()}")
            print(f"   Created: {req['created_at'][:19]}")
            print(f"   Request ID: {req['request_id']}")
        
        input("\nPress Enter to continue...")
    
    def handle_accept_request(self):
        """Handle volunteer accepting a help request."""
        requests = self.clinic_manager.get_open_help_requests()
        
        if not requests:
            print("No open help requests to accept.")
            return
        
        print("\n" + "-"*40)
        print("        ACCEPT HELP REQUEST")
        print("-"*40)
        
        # Show requests with numbers
        for i, req in enumerate(requests, 1):
            print(f"{i}. {req['topic']} - {req['student_name']} ({req['urgency']})")
        
        try:
            choice = int(input(f"\nSelect request to accept (1-{len(requests)}): "))
            if 1 <= choice <= len(requests):
                selected_request = requests[choice - 1]
                session_id = self.clinic_manager.accept_help_request(selected_request['request_id'])
                
                if session_id:
                    print(f"\nRequest accepted successfully!")
                    print(f"Session ID: {session_id}")
                    print("You can now start the session when ready.")
                else:
                    print("Failed to accept request.")
            else:
                print("Invalid selection.")
        except ValueError:
            print("Please enter a valid number.")
    
    def show_user_sessions(self):
        """Show sessions for the current user."""
        sessions = self.clinic_manager.get_user_sessions()
        
        print("\n" + "-"*50)
        print("           MY SESSIONS")
        print("-"*50)
        
        if not sessions:
            print("No sessions found.")
            input("\nPress Enter to continue...")
            return
        
        # Group sessions by status
        scheduled = [s for s in sessions if s.status == "scheduled"]
        in_progress = [s for s in sessions if s.status == "in_progress"]
        completed = [s for s in sessions if s.status == "completed"]
        cancelled = [s for s in sessions if s.status == "cancelled"]
        
        if in_progress:
            print("\n📍 IN PROGRESS:")
            for session in in_progress:
                self.display_session_info(session)
        
        if scheduled:
            print("\n📅 SCHEDULED:")
            for session in scheduled:
                self.display_session_info(session)
        
        if completed:
            print("\n✅ COMPLETED:")
            for session in completed[-5:]:  # Show last 5 completed
                self.display_session_info(session)
        
        if cancelled:
            print("\n❌ CANCELLED:")
            for session in cancelled[-3:]:  # Show last 3 cancelled
                self.display_session_info(session)
        
        input("\nPress Enter to continue...")
    
    def display_session_info(self, session: Session):
        """Display information about a session."""
        # Get participant names
        student = self.clinic_manager.users.get(session.student_id)
        volunteer = self.clinic_manager.users.get(session.volunteer_id)
        
        student_name = student.full_name if student else "Unknown"
        volunteer_name = volunteer.full_name if volunteer else "Unknown"
        
        print(f"   • {session.topic}")
        print(f"     Student: {student_name} | Volunteer: {volunteer_name}")
        print(f"     Status: {session.status.title()} | Scheduled: {session.scheduled_time.strftime('%Y-%m-%d %H:%M')}")
        if session.student_rating:
            print(f"     Student Rating: {session.student_rating}/5")
        if session.volunteer_rating:
            print(f"     Volunteer Rating: {session.volunteer_rating}/5")
        print(f"     ID: {session.session_id}")
        print()
    
    def handle_session_management(self):
        """Handle starting/ending sessions for volunteers."""
        sessions = self.clinic_manager.get_user_sessions()
        active_sessions = [s for s in sessions if s.status in ["scheduled", "in_progress"]]
        
        if not active_sessions:
            print("No active sessions to manage.")
            return
        
        print("\n" + "-"*40)
        print("        SESSION MANAGEMENT")
        print("-"*40)
        
        for i, session in enumerate(active_sessions, 1):
            status_action = "Start" if session.status == "scheduled" else "End"
            print(f"{i}. {status_action} - {session.topic} ({session.status})")
        
        try:
            choice = int(input(f"\nSelect session (1-{len(active_sessions)}): "))
            if 1 <= choice <= len(active_sessions):
                session = active_sessions[choice - 1]
                
                if session.status == "scheduled":
                    if self.clinic_manager.start_session(session.session_id):
                        print("Session started successfully!")
                    else:
                        print("Failed to start session.")
                elif session.status == "in_progress":
                    notes = input("Session notes (optional): ").strip()
                    if self.clinic_manager.end_session(session.session_id, notes):
                        print("Session ended successfully!")
                    else:
                        print("Failed to end session.")
            else:
                print("Invalid selection.")
        except ValueError:
            print("Please enter a valid number.")
    
    def handle_session_rating(self):
        """Handle rating a completed session."""
        sessions = self.clinic_manager.get_user_sessions()
        completed_sessions = [s for s in sessions if s.status == "completed"]
        
        # Filter sessions that haven't been rated by current user
        user = self.clinic_manager.current_user
        if isinstance(user, Student):
            unrated = [s for s in completed_sessions if s.student_rating is None]
        else:
            unrated = [s for s in completed_sessions if s.volunteer_rating is None]
        
        if not unrated:
            print("No sessions available for rating.")
            return
        
        print("\n" + "-"*40)
        print("          RATE SESSION")
        print("-"*40)
        
        for i, session in enumerate(unrated, 1):
            print(f"{i}. {session.topic} - {session.scheduled_time.strftime('%Y-%m-%d')}")
        
        try:
            choice = int(input(f"\nSelect session to rate (1-{len(unrated)}): "))
            if 1 <= choice <= len(unrated):
                session = unrated[choice - 1]
                
                rating = int(input("Rating (1-5): "))
                feedback = input("Feedback (optional): ").strip()
                
                if self.clinic_manager.rate_session(session.session_id, rating, feedback):
                    print("Rating submitted successfully!")
                else:
                    print("Failed to submit rating.")
            else:
                print("Invalid selection.")
        except ValueError:
            print("Please enter valid numbers.")
    
    def show_available_volunteers_detailed(self):
        """Show detailed list of available volunteers."""
        volunteers = self.clinic_manager.get_available_volunteers()
        
        print("\n" + "-"*50)
        print("           AVAILABLE VOLUNTEERS")
        print("-"*50)
        
        if not volunteers:
            print("No volunteers are currently available.")
            input("\nPress Enter to continue...")
            return
        
        for volunteer in volunteers:
            print(f"\n👨‍💻 {volunteer.full_name}")
            print(f"   Experience: {volunteer.experience_level.title()}")
            print(f"   Rating: {volunteer.rating:.1f}/5.0 ({volunteer.total_ratings} ratings)")
            if volunteer.expertise_areas:
                print(f"   Expertise: {', '.join(volunteer.expertise_areas)}")
            if volunteer.programming_languages:
                print(f"   Languages: {', '.join(volunteer.programming_languages)}")
            print(f"   Sessions Conducted: {len(volunteer.sessions_conducted)}")
        
        input("\nPress Enter to continue...")
    
    def update_learning_goals(self):
        """Update student learning goals."""
        if not isinstance(self.clinic_manager.current_user, Student):
            return
        
        student = self.clinic_manager.current_user
        
        print("\n" + "-"*40)
        print("       UPDATE LEARNING GOALS")
        print("-"*40)
        
        print("Current learning goals:")
        for i, goal in enumerate(student.learning_goals, 1):
            print(f"{i}. {goal}")
        
        print("\nOptions:")
        print("1. Add new goal")
        print("2. Remove goal")
        print("3. Back to menu")
        
        choice = input("Select option (1-3): ").strip()
        
        if choice == "1":
            new_goal = input("Enter new learning goal: ").strip()
            if new_goal:
                student.add_learning_goal(new_goal)
                self.clinic_manager.save_data()
                print("Learning goal added successfully!")
        elif choice == "2" and student.learning_goals:
            try:
                goal_num = int(input(f"Goal number to remove (1-{len(student.learning_goals)}): "))
                if 1 <= goal_num <= len(student.learning_goals):
                    removed_goal = student.learning_goals[goal_num - 1]
                    student.remove_learning_goal(removed_goal)
                    self.clinic_manager.save_data()
                    print("Learning goal removed successfully!")
                else:
                    print("Invalid goal number.")
            except ValueError:
                print("Please enter a valid number.")
    
    def update_volunteer_availability(self):
        """Update volunteer availability status."""
        if not isinstance(self.clinic_manager.current_user, Volunteer):
            return
        
        volunteer = self.clinic_manager.current_user
        
        print("\n" + "-"*40)
        print("       UPDATE AVAILABILITY")
        print("-"*40)
        
        current_status = "Available" if volunteer.is_available else "Not Available"
        print(f"Current status: {current_status}")
        
        new_status = input("Set availability (y/n): ").strip().lower()
        if new_status in ['y', 'yes']:
            volunteer.set_availability(True)
            print("Status updated: Available")
        elif new_status in ['n', 'no']:
            volunteer.set_availability(False)
            print("Status updated: Not Available")
        else:
            print("Invalid input.")
            return
        
        self.clinic_manager.save_data()
    
    def update_volunteer_expertise(self):
        """Update volunteer expertise areas."""
        if not isinstance(self.clinic_manager.current_user, Volunteer):
            return
        
        volunteer = self.clinic_manager.current_user
        
        print("\n" + "-"*40)
        print("        UPDATE EXPERTISE")
        print("-"*40)
        
        print("Current expertise areas:")
        for i, area in enumerate(volunteer.expertise_areas, 1):
            print(f"{i}. {area}")
        
        print("\nCurrent programming languages:")
        for i, lang in enumerate(volunteer.programming_languages, 1):
            print(f"{i}. {lang}")
        
        print("\nOptions:")
        print("1. Add expertise area")
        print("2. Add programming language")
        print("3. Remove expertise area")
        print("4. Remove programming language")
        print("5. Back to menu")
        
        choice = input("Select option (1-5): ").strip()
        
        if choice == "1":
            new_area = input("Enter new expertise area: ").strip()
            if new_area:
                volunteer.add_expertise_area(new_area)
                self.clinic_manager.save_data()
                print("Expertise area added successfully!")
        elif choice == "2":
            new_lang = input("Enter programming language: ").strip()
            if new_lang:
                volunteer.add_programming_language(new_lang)
                self.clinic_manager.save_data()
                print("Programming language added successfully!")
        elif choice == "3" and volunteer.expertise_areas:
            try:
                area_num = int(input(f"Area number to remove (1-{len(volunteer.expertise_areas)}): "))
                if 1 <= area_num <= len(volunteer.expertise_areas):
                    removed_area = volunteer.expertise_areas[area_num - 1]
                    volunteer.remove_expertise_area(removed_area)
                    self.clinic_manager.save_data()
                    print("Expertise area removed successfully!")
                else:
                    print("Invalid area number.")
            except ValueError:
                print("Please enter a valid number.")
        elif choice == "4" and volunteer.programming_languages:
            try:
                lang_num = int(input(f"Language number to remove (1-{len(volunteer.programming_languages)}): "))
                if 1 <= lang_num <= len(volunteer.programming_languages):
                    removed_lang = volunteer.programming_languages[lang_num - 1]
                    volunteer.programming_languages.remove(removed_lang)
                    self.clinic_manager.save_data()
                    print("Programming language removed successfully!")
                else:
                    print("Invalid language number.")
            except ValueError:
                print("Please enter a valid number.")
    
    def show_student_profile(self):
        """Show student profile information."""
        if not isinstance(self.clinic_manager.current_user, Student):
            return
        
        student = self.clinic_manager.current_user
        
        print("\n" + "-"*50)
        print("           STUDENT PROFILE")
        print("-"*50)
        print(f"Name: {student.full_name}")
        print(f"Username: {student.username}")
        print(f"Email: {student.email}")
        print(f"Skill Level: {student.skill_level.title()}")
        print(f"Member Since: {student.created_at.strftime('%Y-%m-%d')}")
        print(f"Sessions Completed: {len(student.completed_sessions)}")
        
        if student.learning_goals:
            print(f"\nLearning Goals:")
            for goal in student.learning_goals:
                print(f"  • {goal}")
        
        if student.preferred_languages:
            print(f"\nPreferred Languages:")
            for lang in student.preferred_languages:
                print(f"  • {lang}")
        
        input("\nPress Enter to continue...")
    
    def show_volunteer_profile(self):
        """Show volunteer profile information."""
        if not isinstance(self.clinic_manager.current_user, Volunteer):
            return
        
        volunteer = self.clinic_manager.current_user
        
        print("\n" + "-"*50)
        print("          VOLUNTEER PROFILE")
        print("-"*50)
        print(f"Name: {volunteer.full_name}")
        print(f"Username: {volunteer.username}")
        print(f"Email: {volunteer.email}")
        print(f"Experience Level: {volunteer.experience_level.title()}")
        print(f"Rating: {volunteer.rating:.1f}/5.0 ({volunteer.total_ratings} ratings)")
        print(f"Sessions Conducted: {len(volunteer.sessions_conducted)}")
        print(f"Member Since: {volunteer.created_at.strftime('%Y-%m-%d')}")
        print(f"Currently Available: {'Yes' if volunteer.is_available else 'No'}")
        
        if volunteer.expertise_areas:
            print(f"\nExpertise Areas:")
            for area in volunteer.expertise_areas:
                print(f"  • {area}")
        
        if volunteer.programming_languages:
            print(f"\nProgramming Languages:")
            for lang in volunteer.programming_languages:
                print(f"  • {lang}")
        
        input("\nPress Enter to continue...")

    def handle_calendar_integration(self):
        """Handle calendar integration options."""
        print("\n" + "-"*50)
        print("         CALENDAR INTEGRATION")
        print("-"*50)

        # Check calendar status
        calendar_info = self.clinic_manager.get_calendar_info()
        print(f"Status: {calendar_info['message']}")

        if calendar_info['status'] == 'not_available':
            print("\nGoogle Calendar integration is not available.")
            print("Please install the required dependencies:")
            print("pip install -r requirements.txt")
            input("\nPress Enter to continue...")
            return

        if calendar_info['status'] == 'not_authenticated':
            print("\nCalendar Integration Options:")
            print("1. Authenticate with Google Calendar")
            print("2. Back to main menu")

            choice = input("Select option (1-2): ").strip()

            if choice == "1":
                self.authenticate_calendar()
        else:
            print(f"Authenticated as: {calendar_info.get('authenticated_user', 'Unknown')}")
            print(f"Calendar ID: {calendar_info.get('calendar_id', 'Unknown')}")

            print("\nCalendar Options:")
            print("1. View Upcoming Sessions")
            print("2. Schedule Session with Calendar")
            print("3. Re-authenticate")
            print("4. Back to main menu")

            choice = input("Select option (1-4): ").strip()

            if choice == "1":
                self.view_upcoming_calendar_sessions()
            elif choice == "2":
                self.schedule_session_with_calendar()
            elif choice == "3":
                self.authenticate_calendar()

    def authenticate_calendar(self):
        """Handle Google Calendar authentication."""
        user = self.clinic_manager.current_user
        if not user:
            print("No user logged in.")
            return

        print(f"\nAuthenticating calendar for: {user.email}")
        print("This will open a browser window for Google authentication.")
        print("Please make sure you're using a WeThinkCode email address.")

        confirm = input("Continue? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes']:
            return

        try:
            success = self.clinic_manager.authenticate_calendar(user.email)
            if success:
                print("✓ Calendar authentication successful!")
                print("You can now schedule sessions with Google Calendar.")
            else:
                print("✗ Calendar authentication failed.")
                print("Please ensure you're using a WeThinkCode email address.")
        except Exception as e:
            print(f"Authentication error: {e}")

        input("\nPress Enter to continue...")

    def view_upcoming_calendar_sessions(self):
        """View upcoming sessions from Google Calendar."""
        print("\n" + "-"*50)
        print("       UPCOMING CALENDAR SESSIONS")
        print("-"*50)

        try:
            sessions = self.clinic_manager.get_upcoming_calendar_sessions(7)

            if not sessions:
                print("No upcoming sessions found in the next 7 days.")
            else:
                for i, session in enumerate(sessions, 1):
                    print(f"\n{i}. {session.get('summary', 'Unknown Session')}")
                    print(f"   Start: {session.get('start_time', 'Unknown')}")
                    print(f"   End: {session.get('end_time', 'Unknown')}")
                    if session.get('attendees'):
                        print(f"   Attendees: {', '.join(session['attendees'])}")
                    if session.get('session_id'):
                        print(f"   Session ID: {session['session_id']}")

        except Exception as e:
            print(f"Error retrieving calendar sessions: {e}")

        input("\nPress Enter to continue...")

    def schedule_session_with_calendar(self):
        """Schedule a session with Google Calendar."""
        print("\n" + "-"*40)
        print("    SCHEDULE SESSION WITH CALENDAR")
        print("-"*40)

        # Get user's sessions that can be scheduled
        user_sessions = self.clinic_manager.get_user_sessions()
        scheduled_sessions = [s for s in user_sessions if s.status == "scheduled" and not s.calendar_event_id]

        if not scheduled_sessions:
            print("No sessions available for calendar scheduling.")
            print("Sessions must be in 'scheduled' status and not already have calendar events.")
            input("\nPress Enter to continue...")
            return

        print("Sessions available for calendar scheduling:")
        for i, session in enumerate(scheduled_sessions, 1):
            print(f"{i}. {session.topic} - {session.scheduled_time.strftime('%Y-%m-%d %H:%M')}")

        try:
            choice = int(input(f"\nSelect session (1-{len(scheduled_sessions)}): "))
            if 1 <= choice <= len(scheduled_sessions):
                session = scheduled_sessions[choice - 1]

                print(f"\nScheduling session: {session.topic}")
                print(f"Current time: {session.scheduled_time.strftime('%Y-%m-%d %H:%M')}")

                # Ask if user wants to change the time
                change_time = input("Change scheduled time? (y/n): ").strip().lower()

                scheduled_time = session.scheduled_time
                if change_time in ['y', 'yes']:
                    scheduled_time = self.get_datetime_input("Enter new date and time")

                # Ask for duration
                duration_input = input("Session duration in minutes (default 60): ").strip()
                duration = 60
                if duration_input:
                    try:
                        duration = int(duration_input)
                    except ValueError:
                        print("Invalid duration, using default 60 minutes.")

                # Schedule with calendar
                success = self.clinic_manager.schedule_session_with_calendar(
                    session.session_id, scheduled_time, duration
                )

                if success:
                    print("✓ Session scheduled with Google Calendar!")
                    print("Calendar invitations have been sent to participants.")
                else:
                    print("✗ Failed to schedule session with calendar.")
            else:
                print("Invalid selection.")

        except ValueError:
            print("Please enter a valid number.")
        except Exception as e:
            print(f"Error scheduling session: {e}")

        input("\nPress Enter to continue...")

    def get_datetime_input(self, prompt: str):
        """Get datetime input from user."""
        while True:
            try:
                date_str = input(f"{prompt} (YYYY-MM-DD HH:MM): ").strip()
                return datetime.strptime(date_str, "%Y-%m-%d %H:%M")
            except ValueError:
                print("Invalid format. Please use YYYY-MM-DD HH:MM (e.g., 2024-01-15 14:30)")
                continue
