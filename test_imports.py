#!/usr/bin/env python3
"""
Test imports for the slot-based system.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, '.')

def test_basic_imports():
    """Test basic imports."""
    print("Testing basic imports...")
    
    try:
        from models.time_slot import TimeSlot
        print("✓ TimeSlot imported")
        
        from models.slot_manager import SlotManager
        print("✓ SlotManager imported")
        
        from utils.slot_display import SlotDisplayManager
        print("✓ SlotDisplayManager imported")
        
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_clinic_manager_import():
    """Test clinic manager import."""
    print("Testing ClinicManager import...")
    
    try:
        from models.clinic_manager import ClinicManager
        print("✓ ClinicManager imported")
        return True
    except Exception as e:
        print(f"✗ ClinicManager import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_slot_creation():
    """Test creating a TimeSlot."""
    print("Testing TimeSlot creation...")
    
    try:
        from models.time_slot import TimeSlot
        from datetime import datetime, timedelta
        
        start_time = datetime.now() + timedelta(hours=1)
        slot = TimeSlot("vol_123", start_time, "Test Topic")
        
        print(f"✓ TimeSlot created: {slot.slot_id[:8]}...")
        print(f"✓ Status: {slot.status}")
        print(f"✓ Time: {slot.get_formatted_time()}")
        
        return True
    except Exception as e:
        print(f"✗ TimeSlot creation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run import tests."""
    print("Code Clinics Import Test")
    print("=" * 30)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("ClinicManager Import", test_clinic_manager_import),
        ("TimeSlot Creation", test_time_slot_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\nResults: {passed}/{total} tests passed")
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
