#!/usr/bin/env python3
"""
Test script for Google Calendar integration in Code Clinics application.
"""

import os
import sys
from datetime import datetime, timedelta
from models.clinic_manager import ClinicManager


def test_calendar_integration():
    """Test the calendar integration functionality."""
    print("Testing Code Clinics Calendar Integration...")
    print("=" * 60)
    
    # Initialize clinic manager
    clinic_manager = ClinicManager()
    
    print("\n1. Testing Calendar Availability:")
    calendar_info = clinic_manager.get_calendar_info()
    print(f"   Status: {calendar_info['status']}")
    print(f"   Message: {calendar_info['message']}")
    
    if calendar_info['status'] == 'not_available':
        print("\n❌ Calendar integration not available.")
        print("   Please install dependencies: pip install -r requirements.txt")
        return False
    
    print("\n2. Testing User Registration with WeThinkCode Domains:")
    
    # Test admin registration
    admin_success = clinic_manager.register_user(
        username="admin_user",
        email="<EMAIL>",
        full_name="Admin User",
        role="volunteer",
        experience_level="expert",
        expertise_areas=["System Administration", "Python", "Project Management"]
    )
    print(f"   Admin registration: {'✓ Success' if admin_success else '✗ Failed'}")
    
    # Test student registration
    student_success = clinic_manager.register_user(
        username="test_student_wtc",
        email="<EMAIL>",
        full_name="Test Student WTC",
        role="student",
        skill_level="beginner",
        learning_goals=["Learn Python", "Understand OOP", "Calendar Integration"]
    )
    print(f"   Student registration: {'✓ Success' if student_success else '✗ Failed'}")
    
    # Test volunteer registration
    volunteer_success = clinic_manager.register_user(
        username="test_volunteer_wtc",
        email="<EMAIL>",
        full_name="Test Volunteer WTC",
        role="volunteer",
        experience_level="advanced",
        expertise_areas=["Python", "Calendar APIs", "Web Development"]
    )
    print(f"   Volunteer registration: {'✓ Success' if volunteer_success else '✗ Failed'}")
    
    # Test invalid domain registration
    invalid_success = clinic_manager.register_user(
        username="invalid_user",
        email="<EMAIL>",
        full_name="Invalid User",
        role="student"
    )
    print(f"   Invalid domain registration: {'✓ Correctly rejected' if not invalid_success else '✗ Should have failed'}")
    
    print("\n3. Testing Session Creation and Calendar Scheduling:")
    
    # Login as student and create help request
    if clinic_manager.login_user("test_student_wtc"):
        print("   ✓ Student logged in successfully")
        
        request_id = clinic_manager.create_help_request(
            topic="Google Calendar Integration",
            description="Need help understanding how to integrate Google Calendar API with Python applications",
            urgency="normal"
        )
        
        if request_id:
            print(f"   ✓ Help request created: {request_id}")
            
            # Logout and login as volunteer
            clinic_manager.logout_user()
            
            if clinic_manager.login_user("test_volunteer_wtc"):
                print("   ✓ Volunteer logged in successfully")
                
                # Accept the help request
                session_id = clinic_manager.accept_help_request(request_id)
                
                if session_id:
                    print(f"   ✓ Session created: {session_id}")
                    
                    # Test calendar scheduling
                    print("\n4. Testing Calendar Scheduling:")
                    
                    # Schedule session for tomorrow at 2 PM
                    tomorrow_2pm = datetime.now().replace(hour=14, minute=0, second=0, microsecond=0) + timedelta(days=1)
                    
                    print(f"   Attempting to schedule session for: {tomorrow_2pm}")
                    print("   Note: This requires Google Calendar authentication")
                    
                    # This would require actual authentication in a real scenario
                    print("   ⚠️  Calendar authentication required for full testing")
                    print("   ⚠️  Run the main application and use Calendar Integration menu for full test")
                    
                else:
                    print("   ✗ Failed to create session")
            else:
                print("   ✗ Volunteer login failed")
        else:
            print("   ✗ Failed to create help request")
    else:
        print("   ✗ Student login failed")
    
    print("\n5. Testing Calendar Manager Functionality:")
    
    if clinic_manager.calendar_manager:
        print("   ✓ Calendar manager initialized")
        
        # Test domain validation
        test_emails = [
            "<EMAIL>",  # Admin - should pass
            "<EMAIL>",  # Student domain - should pass
            "<EMAIL>",  # Staff domain - should pass
            "<EMAIL>",  # Invalid domain - should fail
            "<EMAIL>"  # Invalid domain - should fail
        ]
        
        print("   Testing email domain validation:")
        for email in test_emails:
            is_authorized = clinic_manager.calendar_manager._is_authorized_user(email)
            status = "✓ Authorized" if is_authorized else "✗ Rejected"
            print(f"     {email}: {status}")
    else:
        print("   ✗ Calendar manager not available")
    
    print("\n6. Data Persistence Test:")
    print(f"   Total users: {len(clinic_manager.users)}")
    print(f"   Total sessions: {len(clinic_manager.sessions)}")
    print(f"   Total help requests: {len(clinic_manager.help_requests)}")
    
    # Display registered users
    print("\n7. Registered Users:")
    for user in clinic_manager.users.values():
        print(f"   • {user.full_name} ({user.email}) - {user.get_role()}")
    
    print("\n" + "=" * 60)
    print("Calendar Integration Test Summary:")
    print("✓ Calendar integration module loaded")
    print("✓ WeThinkCode domain validation working")
    print("✓ User registration with domain restrictions")
    print("✓ Session creation workflow")
    print("⚠️  Full calendar testing requires Google authentication")
    print("\nTo complete testing:")
    print("1. Run: pip install -r requirements.txt")
    print("2. Run: python3 main.py")
    print("3. Register with a WeThinkCode email")
    print("4. Use Calendar Integration menu option")
    print("5. Authenticate with Google Calendar")
    print("=" * 60)
    
    return True


def test_calendar_authentication_flow():
    """Test the calendar authentication flow (requires user interaction)."""
    print("\n" + "=" * 60)
    print("INTERACTIVE CALENDAR AUTHENTICATION TEST")
    print("=" * 60)
    
    clinic_manager = ClinicManager()
    
    if not clinic_manager.calendar_manager:
        print("❌ Calendar manager not available")
        return False
    
    print("This test requires manual authentication with Google Calendar.")
    print("Make sure you have a WeThinkCode email address ready.")
    
    test_email = input("\nEnter your WeThinkCode email for testing: ").strip()
    
    if not test_email:
        print("No email provided, skipping authentication test.")
        return False
    
    # Validate email domain
    if not clinic_manager.calendar_manager._is_authorized_user(test_email):
        print(f"❌ Email {test_email} is not from an authorized domain")
        return False
    
    print(f"✓ Email {test_email} is from an authorized domain")
    
    confirm = input("Proceed with Google Calendar authentication? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("Authentication test cancelled.")
        return False
    
    try:
        print("Starting authentication process...")
        success = clinic_manager.authenticate_calendar(test_email)
        
        if success:
            print("✓ Calendar authentication successful!")
            
            # Test calendar operations
            calendar_info = clinic_manager.get_calendar_info()
            print(f"Calendar ID: {calendar_info.get('calendar_id')}")
            print(f"Authenticated User: {calendar_info.get('authenticated_user')}")
            
            # Test upcoming sessions
            upcoming = clinic_manager.get_upcoming_calendar_sessions(7)
            print(f"Upcoming sessions in next 7 days: {len(upcoming)}")
            
            return True
        else:
            print("❌ Calendar authentication failed")
            return False
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return False


if __name__ == "__main__":
    print("Code Clinics Calendar Integration Test Suite")
    print("=" * 60)
    
    # Run basic tests
    basic_success = test_calendar_integration()
    
    if basic_success:
        print("\nBasic tests completed successfully!")
        
        # Ask if user wants to run interactive authentication test
        interactive = input("\nRun interactive calendar authentication test? (y/n): ").strip().lower()
        if interactive in ['y', 'yes']:
            test_calendar_authentication_flow()
    
    print("\nTest suite completed!")
